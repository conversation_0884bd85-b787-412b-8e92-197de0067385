.discounts {
  &__wrapper {
    padding: 2.4rem;
    background-color: $color-white;

    &:not(:last-child) {
      margin-bottom: 2.4rem;
    }
  }

  &__header {
    margin-bottom: 2.4rem;
  }

  &__form-wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 2rem;

    @include respond(470px) {
      grid-template-columns: 1fr;
    }

    .input {
      &:not(:last-child) {
        margin-bottom: 0;
      }
    }
  }

  &__btn-wrapper {
    grid-column: 1/-1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

// Coupon status styles
.coupon-status {
  padding: 0.4rem 0.8rem;
  border-radius: 0.4rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: uppercase;

  &.active {
    background-color: #d4edda;
    color: #155724;
  }

  &.inactive {
    background-color: #f8d7da;
    color: #721c24;
  }

  &.expired {
    background-color: #d1ecf1;
    color: #0c5460;
  }
}

// Loading and empty states
.loading-container, .empty-container {
  padding: 4rem 2rem;
  text-align: center;

  .loading-message, .empty-message {
    color: $color-gray;

    h3 {
      margin-bottom: 1rem;
      color: $color-black;
    }

    p {
      margin: 0;
    }
  }
}
