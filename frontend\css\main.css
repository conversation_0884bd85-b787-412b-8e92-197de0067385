@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap");
body {
  font-family: "Roboto", sans-serif;
  font-size: 2rem;
  line-height: 1.5;
}

.xxxl {
  font-size: clamp(8rem, 10vw + 1rem, 12rem);
  line-height: 1.3;
}

.xxl {
  font-size: clamp(6rem, 8vw + 1rem, 8rem);
  line-height: 1.3;
}

.xl {
  font-size: clamp(4.8rem, 6vw + 1rem, 6rem);
  line-height: 1.3;
}

.l {
  font-size: clamp(3.6rem, 4.8vw + 1rem, 4.8rem);
  line-height: 1.3;
}

.m {
  font-size: clamp(2.8rem, 3.6vw + 1rem, 3.6rem);
  line-height: 1.5;
}

.s {
  font-size: 2.4rem;
  line-height: 1.7;
}

.copy {
  font-size: 2rem;
  line-height: 1.5;
}

*,
*::after,
*::before {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
}

html {
  font-size: 62.5%;
}
@media screen and (max-width: 64em) {
  html {
    font-size: 56.25%;
  }
}
@media screen and (max-width: 48em) {
  html {
    font-size: 50%;
  }
}

body {
  box-sizing: border-box;
  background-color: #fff;
  overflow-x: hidden;
}

::selection {
  background-color: #575fcf;
  color: #fff;
}

a {
  text-decoration: none;
}

a,
button {
  display: inline-block;
}

button,
input,
textarea {
  outline: none;
  border: none;
  font-family: inherit;
  font-size: inherit;
}

ol,
ul {
  list-style: none;
}

img {
  display: block;
  max-width: 100%;
  border: 0;
  object-fit: cover;
}

.bold {
  font-weight: 700;
}

.normal {
  font-weight: 400;
}

.light {
  font-weight: 300;
}

.container {
  width: 100%;
  max-width: 127.8rem;
  margin: 0 auto;
}

.brand {
  color: #575fcf;
}

.btn {
  display: block;
  position: relative;
  z-index: 1;
  padding: 1.6rem 3rem;
  text-align: center;
  font-weight: 700;
  overflow: hidden;
  cursor: pointer;
}
.btn--primary {
  background-color: #575fcf;
  color: #fff;
}
.btn--primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-color: #3c40c6;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.2s ease-in-out;
}
.btn--primary:hover::before {
  transform: scaleX(1);
}
.btn--wishlist {
  padding: 1.6rem;
  background-color: #000;
}
.btn--wishlist:hover {
  background-color: #575fcf;
}
.btn .btn:disabled {
  opacity: 0.3;
  pointer-events: none;
}

.input:not(:last-child) {
  margin-bottom: 1.6rem;
}
.input--radio:not(:last-child) {
  margin-bottom: 0.4rem;
}
.input--radio .input__label {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.8rem;
}
.input--radio .input__control {
  width: max-content;
}
.input__label {
  display: block;
}
.input__control {
  display: block;
  width: 100%;
  padding: 2rem;
  background-color: #f6f6f6;
  border: none;
  font-size: inherit;
}

.link {
  background-color: transparent;
  color: #575fcf;
  cursor: pointer;
}
.link:hover {
  color: #3c40c6;
}

.toast {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 4;
  max-width: 800px;
  padding: 10px 20px;
  margin: auto;
  background: #cccccc;
  font-family: inherit;
  text-align: center;
  color: #333333;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s, top 0.2s, visibility 0.2s;
  border-radius: 3px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}
.toast--visible {
  bottom: 20px;
  opacity: 1;
  visibility: visible;
}
.toast--success {
  background: #00c02b;
  border-color: #009d23;
  color: #ffffff;
}
.toast--error {
  background: #d50000;
  border-color: #ba0000;
  color: #ffffff;
}

.product-card {
  background-color: #fff;
}
.product-card:hover .product-card__wishlist-btn {
  opacity: 1;
  pointer-events: all;
}
.product-card:hover .product-card__cart-btn {
  opacity: 1;
  pointer-events: all;
}
.product-card__img-wrapper {
  position: relative;
  z-index: 1;
  height: 42rem;
  overflow: hidden;
}
.product-card__img-link-wrapper {
  display: block;
  width: 100%;
  height: 100%;
}
.product-card__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top;
}
.product-card__wishlist-btn {
  position: absolute;
  top: 2rem;
  right: 2rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.8);
  cursor: pointer;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s;
}
.product-card__wishlist-btn:hover {
  background-color: #3c40c6;
}
.product-card__cart-btn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1rem 1.2rem;
  background-color: rgba(0, 0, 0, 0.6);
  text-align: center;
  color: #fff;
  font-size: 1.6rem;
  cursor: pointer;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s;
}
.product-card__cart-btn:hover {
  background-color: #575fcf;
}
.product-card__text {
  width: 100%;
  text-align: center;
}
.product-card__icon {
  width: 2.6rem;
}
.product-card__content {
  padding: 1.6rem 2.4rem 1.8rem;
}
.product-card__content .btn {
  width: 100%;
}
.product-card__link {
  display: block;
  color: #575fcf;
}
.product-card__link:hover {
  color: #3c40c6;
}
.product-card__price {
  font-size: 1.8rem;
}

.search-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 4px;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}
.search-suggestions-dropdown::-webkit-scrollbar {
  width: 6px;
}
.search-suggestions-dropdown::-webkit-scrollbar-track {
  background: transparent;
}
.search-suggestions-dropdown::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}
.search-suggestions-dropdown::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.search-suggestion {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
  min-height: 120px;
}
.search-suggestion:last-child {
  border-bottom: none;
}
.search-suggestion:hover, .search-suggestion.active {
  background-color: #f8f9fa;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.search-suggestion:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}
.search-suggestion__image {
  width: 80px;
  height: 80px;
  margin-right: 16px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  flex-shrink: 0;
  border: 1px solid #e0e0e0;
}
.search-suggestion__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}
.search-suggestion:hover .search-suggestion__image img {
  transform: scale(1.05);
}
.search-suggestion__content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.search-suggestion__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}
.search-suggestion__name {
  font-weight: 600;
  font-size: 15px;
  color: #333;
  line-height: 1.3;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.search-suggestion__brand {
  font-size: 12px;
  color: #007bff;
  font-weight: 500;
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
  white-space: nowrap;
}
.search-suggestion__description {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin: 4px 0;
}
.search-suggestion__details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.search-suggestion__category {
  font-size: 12px;
  color: #666;
}
.search-suggestion__category .category-label {
  font-weight: 500;
  color: #555;
}
.search-suggestion__stock {
  font-size: 12px;
}
.search-suggestion__stock .stock-label {
  font-weight: 500;
  color: #555;
}
.search-suggestion__stock.in-stock {
  color: #28a745;
}
.search-suggestion__stock.low-stock {
  color: #ffc107;
}
.search-suggestion__stock.out-of-stock {
  color: #dc3545;
}
.search-suggestion__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  gap: 12px;
}
.search-suggestion__price {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}
.search-suggestion__price .final-price {
  font-weight: 700;
  color: #007bff;
  font-size: 16px;
}
.search-suggestion__price .original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 13px;
}
.search-suggestion__price .discount {
  background: #28a745;
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}
.search-suggestion__rating {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}
.search-suggestion__rating .stars {
  display: flex;
  gap: 1px;
}
.search-suggestion__rating .stars .star {
  color: #ddd;
  font-size: 14px;
}
.search-suggestion__rating .stars .star.filled {
  color: #ffc107;
}
.search-suggestion__rating .rating-value {
  color: #666;
  font-weight: 500;
}

.search-suggestion-view-all {
  padding: 12px 16px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  border-top: 1px solid #e0e0e0;
  margin-top: 4px;
}
.search-suggestion-view-all:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateX(2px);
}
.search-suggestion-view-all .view-all-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
}
.search-suggestion-view-all .view-all-text {
  flex: 1;
}
.search-suggestion-view-all .view-all-arrow {
  font-size: 16px;
  transition: transform 0.2s ease;
}
.search-suggestion-view-all:hover .view-all-arrow {
  transform: translateX(4px);
}

.search-suggestions-loading {
  padding: 16px;
  text-align: center;
  color: #666;
  font-size: 14px;
}
.search-suggestions-loading::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
  vertical-align: middle;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.search-suggestions-empty {
  padding: 16px;
  text-align: center;
  color: #999;
  font-size: 14px;
  font-style: italic;
}

@media (max-width: 768px) {
  .search-suggestions-dropdown {
    max-height: 350px;
    margin-top: 2px;
  }
  .search-suggestion {
    padding: 12px;
    min-height: 100px;
  }
  .search-suggestion__image {
    width: 60px;
    height: 60px;
    margin-right: 12px;
  }
  .search-suggestion__content {
    gap: 6px;
  }
  .search-suggestion__name {
    font-size: 14px;
    -webkit-line-clamp: 1;
  }
  .search-suggestion__brand {
    font-size: 11px;
    padding: 1px 6px;
  }
  .search-suggestion__description {
    font-size: 12px;
    display: none;
  }
  .search-suggestion__details {
    gap: 2px;
  }
  .search-suggestion__category, .search-suggestion__stock {
    font-size: 11px;
  }
  .search-suggestion__footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
  .search-suggestion__price {
    font-size: 13px;
    gap: 6px;
  }
  .search-suggestion__price .final-price {
    font-size: 15px;
  }
  .search-suggestion__price .original-price {
    font-size: 12px;
  }
  .search-suggestion__price .discount {
    font-size: 9px;
    padding: 2px 6px;
  }
  .search-suggestion__rating {
    font-size: 11px;
    gap: 4px;
  }
  .search-suggestion__rating .stars .star {
    font-size: 12px;
  }
}
@media (max-width: 480px) {
  .search-suggestion {
    padding: 10px;
    min-height: 80px;
  }
  .search-suggestion__image {
    width: 50px;
    height: 50px;
    margin-right: 10px;
  }
  .search-suggestion__name {
    font-size: 13px;
  }
  .search-suggestion__brand {
    font-size: 10px;
  }
  .search-suggestion__price .final-price {
    font-size: 14px;
  }
}
@media (prefers-color-scheme: dark) {
  .search-suggestions-dropdown {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  .search-suggestion {
    border-bottom-color: #4a5568;
  }
  .search-suggestion:hover, .search-suggestion.active {
    background-color: #4a5568;
  }
  .search-suggestion__name {
    color: #e2e8f0;
  }
  .search-suggestion__category {
    color: #a0aec0;
  }
  .search-suggestion__price .final-price {
    color: #63b3ed;
  }
  .search-suggestions-loading,
  .search-suggestions-empty {
    color: #a0aec0;
  }
}
.nav {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 8;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
  background-color: #fbfbfb;
}
.nav__announcement {
  position: relative;
  z-index: 3;
  padding: 5px;
  height: 2.8rem;
  background-color: #000;
  font-size: 1.4rem;
  color: #fff;
  text-align: center;
}
.nav__announcement-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.nav__announcement-arrow {
  cursor: pointer;
}
.nav__announcement-arrow:hover {
  color: #575fcf;
}
.nav__announcement-link {
  color: #fff;
}
.nav__announcement-link:hover {
  color: #3c40c6;
}
.nav__upper {
  position: relative;
  z-index: 2;
  padding: 0 2.4rem;
  display: flex;
  justify-content: stretch;
  align-items: stretch;
  background-color: #fff;
  width: 100vw;
  height: 7rem;
}
@media screen and (max-width: 52.5em) {
  .nav__upper {
    height: auto;
  }
}
.nav__upper-wrapper {
  display: grid;
  grid-template-columns: max-content 1fr max-content;
  align-items: center;
  grid-gap: 1rem;
  height: 100%;
}
@media screen and (max-width: 52.5em) {
  .nav__upper-wrapper {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, max-content);
    grid-gap: 0;
  }
}
.nav__logo {
  max-width: 8rem;
  transform: translateY(4px);
}
.nav__secondary {
  display: flex;
  justify-content: flex-end;
  align-items: stretch;
  gap: 2rem;
  height: 100%;
}
@media screen and (max-width: 52.5em) {
  .nav__secondary {
    grid-column: 1/-1;
    grid-row: 2/3;
    padding-top: 1rem;
    border-top: 1px solid #cacaca;
    padding-bottom: 0.4rem;
  }
}
.nav__category-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 3rem;
}
@media screen and (max-width: 34.6875em) {
  .nav__category-list {
    display: none;
  }
}
.nav__category-hidden {
  display: none;
  justify-content: flex-start;
  align-items: center;
  gap: 3rem;
}
@media screen and (max-width: 34.6875em) {
  .nav__category-hidden {
    display: flex;
  }
}
.nav__category-item {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 3;
  height: 100%;
}
.nav__category-item:hover .nav__lower-category {
  font-weight: 700;
}
.nav__category-item:hover .nav__lower {
  opacity: 1;
  pointer-events: all;
}
.nav__category-link {
  color: #575fcf;
}
.nav__category-link:hover {
  color: #3c40c6;
}
.nav__category-item {
  cursor: pointer;
}
.nav__interaction-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
}
@media screen and (max-width: 52.5em) {
  .nav__interaction-wrapper {
    justify-content: flex-end;
  }
}
.nav__search-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 1rem 0;
  background-color: #f6f6f6;
  border: 2px solid #000;
  border-radius: 100px;
  overflow: hidden;
}
@media screen and (max-width: 28.75em) {
  .nav__search-wrapper {
    display: none;
  }
}
.nav__input {
  padding: 0.4rem 2rem;
  width: 100%;
  background-color: #f6f6f6;
}
.nav__search-btn {
  padding: 1rem 2rem;
  background-color: transparent;
  cursor: pointer;
}
.nav__list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.6rem;
}
.nav__list-item {
  display: flex;
  justify-content: center;
  align-items: center;
}
.nav__link {
  color: #000;
}
.nav__link:hover {
  color: #3c40c6;
}
.nav__btn {
  padding: 1rem;
  border-radius: 8px;
}
.nav__btn:hover {
  background-color: #f6f6f6;
}
.nav__btn-icon {
  height: 2rem;
}
.nav__lower {
  position: fixed;
  top: 10rem;
  left: 50%;
  z-index: -1;
  overflow-y: auto;
  height: 95vh;
  width: 90%;
  padding: 2rem 2.4rem 4rem;
  background-color: #f6f6f6;
  color: #000;
  font-size: 1.6rem;
  transform: translateX(-50%);
  opacity: 0;
  pointer-events: none;
}
@media screen and (max-width: 57.5em) {
  .nav__lower {
    width: 100%;
    height: 100vh;
  }
}
.nav__lower-tabs {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 2px solid #cacaca;
}
.nav__lower-tabs-title {
  padding: 2rem;
  background-color: #fbfbfb;
}
.nav__lower-tab {
  padding: 2rem;
  background-color: rgb(227.5, 227.5, 227.5);
}
.nav__lower-tab:hover {
  background-color: #575fcf;
}
.nav__lower-tab--active {
  background-color: #575fcf;
  color: #fff;
}
.nav__lower-grid {
  padding: 2rem 0;
  display: grid;
  grid-template-columns: 24rem 1fr;
  grid-gap: 4rem;
}
@media screen and (max-width: 57.5em) {
  .nav__lower-grid {
    grid-template-columns: 1fr;
  }
}
.nav__lower-category-link {
  color: #000;
}
.nav__lower-category-link:not(:last-child) {
  margin-bottom: 1rem;
}
.nav__lower-category-link:hover {
  color: #3c40c6;
}
.nav__lower-link {
  background-color: transparent;
  color: #000;
}
.nav__lower-link:hover {
  color: #3c40c6;
}
.nav__lower-links-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.6rem;
}
@media screen and (max-width: 41.25em) {
  .nav__lower-links-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
.nav__lower-title {
  font-weight: 700;
}

.footer {
  padding: 6rem 2.4rem 2.4rem;
  background-color: #2c3e50;
  color: #fff;
  font-size: 1.6rem;
}
.footer__upper {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 2.4rem;
}
@media screen and (max-width: 75.9375em) {
  .footer__upper {
    grid-template-columns: 0.5fr 1fr 1fr 2fr;
  }
}
@media screen and (max-width: 56.25em) {
  .footer__upper {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media screen and (max-width: 39.375em) {
  .footer__upper {
    grid-template-columns: 1fr;
  }
}
.footer__title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.2rem;
  cursor: pointer;
}
.footer__title--visible .footer__title-icon {
  transform: rotate(180deg);
}
.footer__title--visible + .footer__reveal {
  max-height: 100rem;
  opacity: 1;
  pointer-events: all;
}
.footer__reveal {
  max-height: 0;
  opacity: 0;
  pointer-events: none;
  transition: max-height 0.3s, opacity 0.3s;
}
.footer__social-links {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
}
.footer__social-icon {
  height: 3.2rem;
  width: 3.2rem;
  color: #fff;
}
.footer__list-item:not(:last-child) {
  margin-bottom: 0.4rem;
}
.footer__link {
  color: #fff;
}
.footer__link:hover {
  color: #575fcf;
}
.footer__tagline {
  margin: 2rem 0;
  text-align: left;
}
.footer__tagline-text {
  margin-bottom: 1rem;
}
.footer__tagline-info-wrapper {
  display: none;
  opacity: 0;
  max-height: 0;
  max-width: 80rem;
  margin-bottom: 3rem;
  transition: opacity 0.3s 0.3s, max-height 0.3s 0.3s;
}
.footer__tagline-info-wrapper--visible {
  display: block;
  max-height: 100rem;
  opacity: 1;
}
.footer__copyright {
  text-align: center;
}

.header {
  position: relative;
  z-index: 1;
  background-color: #fff;
  padding: 14rem 2.4rem 2.4rem;
  color: #000;
  border-bottom: 2px solid #cacaca;
}
@media screen and (max-width: 52.5em) {
  .header {
    padding: 20rem 2.4rem 4rem;
  }
}
.header__bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  height: 100%;
  width: 100%;
  opacity: 0.4;
}

.hero {
  position: relative;
}
.hero__slide {
  background-color: #000;
  padding-top: 8rem;
  height: 68rem;
  color: #fff;
}
@media screen and (max-width: 49.375em) {
  .hero__slide {
    height: 50rem;
  }
}
@media screen and (max-width: 34.375em) {
  .hero__slide {
    height: auto;
  }
}
.hero__bg {
  width: 100%;
  height: 100%;
}
@media screen and (max-width: 34.375em) {
  .hero__bg {
    object-fit: contain;
  }
}
.hero__content {
  max-width: 60rem;
}
.hero__controls {
  position: absolute;
  bottom: 2rem;
  left: 0;
  z-index: 2;
  width: 100%;
  padding: 0 2.4rem;
}
.hero__controls-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 2px;
}
.hero__control {
  width: 5rem;
  height: 1rem;
  background-color: #cacaca;
  border-radius: 2px;
  cursor: pointer;
}
.hero__control:hover {
  background-color: #fbfbfb;
}
.hero__control--active {
  background-color: #575fcf;
  pointer-events: none;
}

.category {
  padding: 6rem 2.4rem;
}
.category__header {
  margin-bottom: 2.4rem;
  text-align: center;
}
.category__grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 1.2rem;
}
@media screen and (max-width: 40.625em) {
  .category__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 20em) {
  .category__grid {
    grid-template-columns: 1fr;
  }
}
.category__card {
  color: #000;
}
.category__card:hover {
  color: #575fcf;
}
.category__card:hover .category__img {
  transform: translateY(-1rem);
}
.category__img {
  height: 42rem;
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}
@media screen and (max-width: 26.25em) {
  .category__img {
    height: 36rem;
  }
}
.category__content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 1.2rem 2rem;
}
.category__read-more-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
.category__link {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  margin-top: 3.6rem;
  padding: 8px 12px 4px 12px;
  background-color: #fbfbfb;
  color: #575fcf;
}
.category__link:hover {
  background-color: #575fcf;
  color: #fff;
}
.category__link:hover .collection__read-icon {
  border-color: #fff;
}
.category__read-icon {
  height: 1rem;
  width: 1rem;
  border-top: 3px solid #575fcf;
  border-right: 3px solid #575fcf;
  transform: rotate(45deg);
}

.featured {
  background-color: #fbfbfb;
  padding: 6rem 2.4rem;
}
.featured__header {
  margin-bottom: 2.4rem;
}
.featured__title-wrapper {
  position: relative;
  z-index: 1;
}
.featured__circle {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  height: 8rem;
  width: auto;
  transform: translateX(-2rem) translateY(-0.8rem);
}
.featured__grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 4px;
}
@media screen and (max-width: 58.4375em) {
  .featured__grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media screen and (max-width: 35.9375em) {
  .featured__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media screen and (max-width: 25.625em) {
  .featured__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
.featured__grid-item {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 1.6rem;
  height: 28rem;
  background-color: #000;
  color: #fff;
  overflow: hidden;
  transition: filter 0.3s;
}
.featured__grid-item--placeholder {
  background-color: transparent;
}
@media screen and (max-width: 58.4375em) {
  .featured__grid-item--placeholder {
    display: none;
  }
}
.featured__grid-item:hover {
  filter: brightness(120%) contrast(106%);
}
.featured__bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  height: 100%;
  width: 100%;
  opacity: 0.5;
}
.featured__discount {
  font-size: 1.6rem;
}

.sellers {
  background-color: #fbfbfb;
  border-top: 2px solid #cacaca;
  padding: 6rem 2.4rem;
}
.sellers__header {
  margin-bottom: 2.2rem;
}
.sellers__grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.2rem;
}
@media screen and (max-width: 40.625em) {
  .sellers__grid {
    grid-template-columns: 1fr;
  }
}

.about {
  padding: 8rem 2.4rem;
}
.about__grid {
  text-align: center;
  max-width: 96rem;
  margin: 0 auto;
}
.about__content-wrapper p:not(:last-child) {
  margin-bottom: 2.4rem;
}
.about__title {
  margin-bottom: 1.6rem;
}
.banner {
  background-color: #fbfbfb;
  padding: 4rem 2.4rem;
}
.banner__carousel {
  position: relative;
  z-index: 1;
}
.banner__img {
  width: 100%;
  height: 40rem;
}
.banner__controls-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
}
.banner__control {
  height: 1rem;
  width: 1rem;
  border-radius: 50%;
  background-color: #cacaca;
  cursor: pointer;
}
.banner__control--active {
  background-color: #575fcf;
}
.banner__control:hover {
  background-color: #fbfbfb;
}

.refer {
  padding: 4rem 2.4rem;
}
.refer__wrapper {
  max-width: 80rem;
  margin: 0 auto;
  padding: 8rem 2.4rem 2.4rem;
  background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url("../img/hero.jpg");
  border-radius: 12px;
  overflow: hidden;
  text-align: center;
  color: #fff;
}
.why {
  padding: 6rem 2.4rem;
  background-color: #fbfbfb;
  text-align: center;
}
.why__header {
  margin-bottom: 1.2rem;
}
.why__content {
  max-width: 80rem;
  margin: 0 auto 3.6rem;
}
.why__grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.6rem;
  max-width: 112rem;
  margin: 0 auto;
}
@media screen and (max-width: 23.75em) {
  .why__grid {
    grid-template-columns: 1fr;
    grid-gap: 0;
  }
}
.why__grid-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 2.4rem 2rem 3rem;
  background-color: #fff;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  color: #000;
}
@media screen and (max-width: 23.75em) {
  .why__grid-item {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  .why__grid-item:nth-child(1) {
    position: relative;
    z-index: 3;
  }
  .why__grid-item:nth-child(2) {
    position: relative;
    z-index: 2;
  }
}
.why__icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 6rem;
  width: 6rem;
  padding: 1rem;
  background-color: #575fcf;
  border-radius: 8px;
}
.why__icon {
  height: 4rem;
  width: auto;
  object-fit: contain;
}

.cta {
  padding: 20rem 2.4rem 6rem;
  background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url("../img/cta.jpg");
  background-size: cover;
  background-position: center;
}
.cta__content {
  max-width: 58rem;
  color: #fff;
}
.cta__btn {
  margin-top: 2rem;
}

.faqs {
  padding: 8rem 2.4rem;
}
.faqs__header {
  margin-bottom: 2.4rem;
}
.faqs__item:not(:last-child) {
  margin-bottom: 2.4rem;
  padding-bottom: 2.4rem;
  border-bottom: 2px solid #9f9f9f;
}
.questions {
  padding: 6rem 2.4rem;
  background-color: #fbfbfb;
}
.questions__wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.products {
  padding: 0 2.4rem 6rem;
}
.products__grid {
  display: grid;
  grid-template-columns: 1fr 4fr;
  grid-gap: 3rem;
}
@media screen and (max-width: 37.5em) {
  .products__grid {
    grid-template-columns: 1fr;
    grid-gap: 0;
    padding: 0 2.4rem;
  }
}
.products__cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.6rem;
  padding: 3.2rem 0;
}
@media screen and (max-width: 83.75em) {
  .products__cards-grid {
    padding: 3.2rem 2.4rem 3.2rem 0;
  }
}
@media screen and (max-width: 57.5em) {
  .products__cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 37.5em) {
  .products__cards-grid {
    padding: 3.2rem 0;
  }
}
@media screen and (max-width: 34.6875em) {
  .products__cards-grid {
    grid-template-columns: 1fr;
  }
}
.products__cards-grid .product-card {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
}

.sidebar {
  padding: 3.2rem 0;
  border-right: 2px solid #f6f6f6;
  font-size: 1.6rem;
}
.sidebar__header {
  padding-bottom: 2rem;
  padding-left: 2rem;
  border-bottom: 2px solid #f6f6f6;
}
@media screen and (max-width: 37.5em) {
  .sidebar__category {
    display: none;
  }
}
.sidebar__category:not(:last-child) {
  border-bottom: 2px solid #f6f6f6;
}
.sidebar__title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 1.6rem 2rem 1.6rem;
  cursor: pointer;
}
.sidebar__title:hover {
  background-color: #f6f6f6;
}
.sidebar__list {
  padding-bottom: 2rem;
  padding-left: 2rem;
}
.sidebar__list-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1.2rem;
  cursor: pointer;
}
.sidebar__list-item:not(:last-child) {
  margin-bottom: 1.2rem;
}
.sidebar__list-item::before {
  content: "";
  display: block;
  height: 6px;
  width: 6px;
  background-color: #cacaca;
  border-radius: 50%;
}
.sidebar__list-item--active {
  font-weight: 700;
}
.sidebar__list-item:hover {
  color: #575fcf;
}

.product {
  padding: 12rem 2.4rem 6rem;
}
@media screen and (max-width: 52.5em) {
  .product {
    padding: 20rem 2.4rem 6rem;
  }
}
.product__grid {
  display: grid;
  grid-template-columns: 1fr 0.9fr;
  grid-gap: 3.6rem;
  margin-bottom: 6rem;
}
@media screen and (max-width: 61.25em) {
  .product__grid {
    grid-template-columns: 1fr 1.2fr;
  }
}
@media screen and (max-width: 50em) {
  .product__grid {
    grid-template-columns: 1fr;
  }
}
.product__img-grid {
  display: grid;
  grid-template-columns: max-content 1fr;
  grid-gap: 12px;
  height: min(70vh, 60rem);
  max-height: 70vh;
}
@media screen and (max-width: 61.25em) {
  .product__img-grid {
    grid-template-columns: 1fr;
    height: max-content;
    max-height: none;
  }
}
.product__thumbnails {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 8rem;
  grid-auto-rows: 8rem;
  grid-gap: 8px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(87, 95, 207, 0.3) transparent;
}
.product__thumbnails::-webkit-scrollbar {
  width: 4px;
}
.product__thumbnails::-webkit-scrollbar-track {
  background: transparent;
}
.product__thumbnails::-webkit-scrollbar-thumb {
  background: rgba(87, 95, 207, 0.3);
  border-radius: 2px;
}
.product__thumbnails::-webkit-scrollbar-thumb:hover {
  background: rgba(87, 95, 207, 0.5);
}
@media screen and (max-width: 61.25em) {
  .product__thumbnails {
    grid-template-columns: repeat(8, 6rem);
    grid-row: 2/3;
    grid-gap: 12px;
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 8px;
  }
  .product__thumbnails::-webkit-scrollbar {
    height: 4px;
  }
}
@media screen and (max-width: 28.125em) {
  .product__thumbnails {
    grid-template-columns: repeat(4, 6rem);
    justify-content: flex-start;
  }
}
@media screen and (max-width: 20em) {
  .product__thumbnails {
    grid-template-columns: repeat(3, 5rem);
    grid-gap: 8px;
  }
}
.product__thumbnail-wrapper {
  width: 6rem;
  height: 8rem;
  background-color: #3c40c6;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}
.product__thumbnail-wrapper:hover {
  border-color: #575fcf;
  transform: scale(1.05);
}
@media screen and (max-width: 20em) {
  .product__thumbnail-wrapper {
    width: 5rem;
    height: 6.5rem;
  }
}
.product__thumbnail {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: all 0.3s ease;
}
.product__thumbnail--selected {
  opacity: 0.7;
}
.product__thumbnail:hover {
  opacity: 0.8;
}
.product__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: zoom-in;
  background-color: #f6f6f6;
}
.product__img:hover {
  transform: scale(1.02);
}
@media screen and (max-width: 61.25em) {
  .product__img {
    object-fit: contain;
    max-height: 50vh;
    height: auto;
    min-height: 30rem;
  }
}
@media screen and (max-width: 37.5em) {
  .product__img {
    max-height: 40vh;
    min-height: 25rem;
  }
}
@media screen and (max-width: 30em) {
  .product__img {
    max-height: 35vh;
    min-height: 20rem;
  }
}
.product__header {
  padding-bottom: 2rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #cacaca;
}
.product__description {
  margin-bottom: 2rem;
}
.product__list {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid #cacaca;
}
.product__list-item {
  text-indent: -12px;
  font-size: 1.8rem;
  margin-left: 1rem;
}
.product__list-item::before {
  content: "-";
  text-indent: -12px;
}
.product__list-item:not(:last-child) {
  margin-bottom: 8px;
}
.product__meta-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 2rem;
}
.product__review-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
}
.product__review-btn {
  padding: 4px 1.2rem;
  background-color: #fbfbfb;
  color: #575fcf;
  cursor: pointer;
}
.product__review-btn:hover {
  background-color: #575fcf;
  color: #fff;
}
.product__info-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2.8rem;
}
.product__category {
  padding: 0.8rem 2rem;
  background-color: #f6f6f6;
}
.product__secondary-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}
.product__options-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
  gap: 2.4rem;
  max-width: unset;
}
.product__colors-selection {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
}
.product__color {
  width: 4.8rem;
  height: 4.8rem;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s;
}
.product__color:hover {
  transform: translateY(-0.4rem);
}
.product__color--selected {
  border-color: #575fcf;
  transform: scale(1.1);
}
.product__sizes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}
.product__sizes-link-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  text-decoration: underline;
}
.product__sizes-link-wrapper:hover {
  color: #575fcf;
}
.product__sizes-icon {
  height: 2.2rem;
  transform: translateY(4px);
}
.product__sizes-selection {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}
.product__size {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 3rem;
  background-color: #fff;
  border: 2px solid #cacaca;
  border-radius: 100px;
  transition: background-color 0.3s;
  cursor: pointer;
}
.product__size:hover {
  background-color: #3c40c6;
  border-color: #3c40c6;
  color: #fff;
}
.product__size--selected {
  background-color: #575fcf;
  border-color: #575fcf;
  color: #fff;
}
.product__size--nostock {
  position: relative;
  overflow: hidden;
  cursor: not-allowed;
  opacity: 0.3;
}
.product__size--nostock:hover {
  background-color: #cacaca;
  color: #000;
}
.product__cart-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  margin-top: 3.2rem;
}
.product__btn {
  width: 100%;
  padding: 1.2rem;
}
.product__delivery-wrapper {
  margin: 2rem 0;
  padding: 3rem;
  background-color: #f6f6f6;
}
.product__delivery-control {
  display: grid;
  grid-template-columns: 1fr max-content;
}
@media screen and (max-width: 31.25em) {
  .product__delivery-control {
    grid-template-columns: 1fr;
  }
}
.product__delivery-input {
  padding: 2rem;
  width: 100%;
  background-color: #fff;
}
.product__delivery-input:focus {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
}
.product__delivery-btn {
  padding: 2rem 3rem;
  background-color: #000;
  color: #fff;
  text-align: center;
  cursor: pointer;
}
.product__delivery-btn:hover {
  background-color: #575fcf;
  color: #fff;
}
.product__usp-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.2rem;
}
@media screen and (max-width: 30em) {
  .product__usp-wrapper {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 30em) {
  .product__usp {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 2rem;
  }
}
.product__usp-icon {
  height: 6rem;
  width: 6rem;
  object-fit: contain;
}
.product__details {
  margin-top: 2rem;
  border-top: 1px solid #cacaca;
}
.product__details-wrapper {
  padding: 2rem;
  cursor: pointer;
}
.product__details-wrapper:hover {
  background-color: #f6f6f6;
}
.product__details-wrapper:not(:last-child) {
  border-bottom: 1px solid #cacaca;
}
.product__details-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}
.product__details-content {
  display: none;
  padding-top: 2rem;
}
.product__rating-summary {
  margin-bottom: 2rem;
  padding: 2rem;
  background-color: #f6f6f6;
  border-radius: 8px;
}
.product__rating-average {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.product__rating-number {
  font-size: 2.4rem;
  font-weight: bold;
  color: #575fcf;
}
.product__rating-stars {
  color: #ffc107;
  font-size: 1.8rem;
}
.product__rating-count {
  color: #9f9f9f;
  font-size: 1.4rem;
}
.product__reviews-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}
.product__review-item {
  padding: 2rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.product__review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}
@media screen and (max-width: 30em) {
  .product__review-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
.product__review-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.product__verified-badge {
  background-color: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 1.2rem;
}
.product__review-rating {
  color: #ffc107;
  font-size: 1.4rem;
}
.product__review-text {
  margin-bottom: 1rem;
  line-height: 1.6;
}
.product__review-location {
  color: #9f9f9f;
  font-size: 1.3rem;
}

.description {
  padding: 6rem 2.4rem;
  background-color: #f6f6f6;
}
.description__info-grid {
  display: grid;
  grid-template-columns: 20rem 1fr;
  grid-gap: 2rem;
  margin-bottom: 3.2rem;
  padding-bottom: 3.2rem;
  border-bottom: 2px solid #000;
}
.description__text {
  max-width: 57rem;
}
.description__sepcs-grid {
  display: grid;
  grid-template-columns: 20rem 1fr;
  grid-gap: 2rem;
}
.description__spec {
  display: grid;
  grid-template-columns: 16rem 1fr;
  grid-gap: 6rem;
}

.related {
  padding: 6rem 2.4rem;
  background-color: #fbfbfb;
}
.related--category {
  border-top: 2px solid #cacaca;
}
.related__header {
  margin-bottom: 3.2rem;
}
.related__grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 2rem;
}
@media screen and (max-width: 43.75em) {
  .related__grid {
    grid-template-columns: 1fr;
  }
}
.related__grid .product-card {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
}

.image-zoom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}
.image-zoom-modal--active {
  opacity: 1;
  visibility: visible;
}

.image-zoom-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.image-zoom-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-zoom-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}
.image-zoom-img:hover {
  transform: scale(1.05);
}

.image-zoom-close {
  position: absolute;
  top: -4rem;
  right: -4rem;
  width: 4rem;
  height: 4rem;
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  font-size: 2.4rem;
  font-weight: bold;
  color: #000;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  z-index: 10001;
}
.image-zoom-close:hover {
  background-color: #fff;
  transform: scale(1.1);
}
@media screen and (max-width: 37.5em) {
  .image-zoom-close {
    top: 1rem;
    right: 1rem;
    width: 3rem;
    height: 3rem;
    font-size: 2rem;
  }
}

.image-zoom-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 10001;
}

.image-zoom-prev,
.image-zoom-next {
  width: 5rem;
  height: 5rem;
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  font-size: 2.4rem;
  font-weight: bold;
  color: #000;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  pointer-events: all;
}
.image-zoom-prev:hover,
.image-zoom-next:hover {
  background-color: #fff;
  transform: scale(1.1);
}
@media screen and (max-width: 37.5em) {
  .image-zoom-prev,
  .image-zoom-next {
    width: 4rem;
    height: 4rem;
    font-size: 2rem;
  }
}

.image-zoom-prev {
  margin-left: -7rem;
}
@media screen and (max-width: 37.5em) {
  .image-zoom-prev {
    margin-left: -5rem;
  }
}

.image-zoom-next {
  margin-right: -7rem;
}
@media screen and (max-width: 37.5em) {
  .image-zoom-next {
    margin-right: -5rem;
  }
}

.cart {
  padding: 8rem 2.4rem;
  background-color: #f6f6f6;
}
.cart__grid {
  display: grid;
  grid-template-columns: 1.4fr 1fr;
  align-items: start;
  grid-gap: 3.2rem;
}
@media screen and (max-width: 50em) {
  .cart__grid {
    grid-template-columns: 1fr;
  }
}
.cart__single {
  display: grid;
  grid-template-columns: 11.2rem 1fr;
  background-color: #fff;
}
.cart__single:not(:last-child) {
  margin-bottom: 1.2rem;
}
.cart__img {
  height: 100%;
  width: 100%;
}
.cart__contents-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 3.2rem;
  padding: 2rem;
}
.cart__content-details {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 0.4rem;
}
.cart__select-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
}
.cart__select {
  padding: 1rem;
  font-size: 1.6rem;
  border: none;
}
.cart__price {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin: 0.4rem 0;
}
.cart__price--discounted {
  font-weight: bold;
  color: #575fcf;
}
.cart__price--original {
  text-decoration: line-through;
  color: #999;
  font-size: 0.9em;
}
.cart__discount-badge {
  background-color: #e74c3c;
  color: white;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.75em;
  font-weight: bold;
}
.cart__interactions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 4px;
}
.cart__delete-btn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: transparent;
  color: red;
  opacity: 0.7;
  background-color: rgb(255, 239.7, 239.7);
}
.cart__wishlist-btn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  background-color: transparent;
  padding: 1rem;
  background-color: #fbfbfb;
}
.cart__total {
  padding: 2.4rem;
  background-color: #fff;
}
.cart__total .btn {
  width: 100%;
}
.cart__discount-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  margin-bottom: 3.2rem;
}
.cart__input-wrapper {
  width: 100%;
}
.cart__input {
  padding: 2rem;
  width: 100%;
  background-color: #f6f6f6;
}
.cart__btn {
  padding: 2rem;
  background-color: #000;
  color: #fff;
  cursor: pointer;
  border: none;
  transition: background-color 0.3s ease;
}
.cart__btn:hover {
  background-color: rgb(25.5, 25.5, 25.5);
}
.cart__btn--remove {
  background-color: #dc3545;
}
.cart__btn--remove:hover {
  background-color: rgb(189.2151898734, 32.7848101266, 47.7721518987);
}
.cart__journey {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding: 2rem;
  background-color: #f6f6f6;
  margin-bottom: 2rem;
}
@media screen and (max-width: 28.75em) {
  .cart__journey {
    grid-template-columns: 1fr;
    grid-gap: 2rem;
  }
}
.cart__milestone {
  width: 100%;
}
@media screen and (max-width: 28.75em) {
  .cart__milestone {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
}
.cart__milestone:last-child .cart__milestone-icon-wrapper::after {
  display: none;
}
.cart__milestone-text {
  padding-right: 1rem;
  width: max-content;
}
.cart__milestone-icon-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
@media screen and (max-width: 28.75em) {
  .cart__milestone-icon-wrapper {
    display: none;
  }
}
.cart__milestone-icon-wrapper::after {
  content: "";
  display: inline-block;
  height: 2px;
  width: 100%;
  background-color: #000;
}
@media screen and (max-width: 28.75em) {
  .cart__milestone-icon-wrapper::after {
    display: none;
  }
}
.cart__milestone-icon {
  height: 2rem;
  width: 2rem;
}
.cart__payment-wrapper {
  margin-bottom: 2.4rem;
}
.cart__info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cart__address-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 1.2rem;
}
@media screen and (max-width: 31.25em) {
  .cart__address-grid {
    grid-template-columns: 1fr;
  }
}
.cart__address {
  padding: 2.4rem;
  background-color: #fff;
}
.cart__address--selected {
  background-color: #575fcf;
  color: #fff;
}
.cart__address--selected .cart__address-icon {
  display: block;
}
.cart__address-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}
.cart__address-icon {
  display: none;
}
.cart__products {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 0.4rem;
  margin-bottom: 3.2rem;
}

.login {
  padding: 16rem 2.4rem 8rem;
  min-height: 100vh;
}
.login__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 3.2rem;
  min-height: calc(100vh - 24rem);
}
@media screen and (max-width: 53.125em) {
  .login__grid {
    grid-template-columns: 1fr;
  }
}
.login__img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.login__form {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding: 5rem;
  background-color: #fff;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.login__title {
  margin-bottom: 3.2rem;
}
.login__info-wrapper {
  margin-top: 2rem;
  text-align: right;
}
.login__info-wrapper:not(:last-child) {
  margin-bottom: 2rem;
}
.signup {
  padding: 16rem 2.4rem 8rem;
  min-height: 100vh;
}
.signup__img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.signup__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 3.2rem;
  min-height: calc(100vh - 24rem);
}
@media screen and (max-width: 53.125em) {
  .signup__grid {
    grid-template-columns: 1fr;
  }
}
.signup__form {
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding: 5rem;
  background-color: #fff;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.signup__title {
  margin-bottom: 3.2rem;
}
.signup__info-wrapper {
  margin-top: 2rem;
  text-align: right;
}
.signup__info-wrapper:not(:last-child) {
  margin-bottom: 2rem;
}
.orders__single {
  display: grid;
  grid-template-columns: max-content 1fr;
}
@media screen and (max-width: 37.5em) {
  .orders__single {
    grid-template-columns: 1fr;
    border: 3px solid #000;
  }
}
.orders__single:not(:last-child) {
  margin-bottom: 3.2rem;
}
.orders__preview-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
@media screen and (max-width: 37.5em) {
  .orders__preview-wrapper {
    gap: 0;
  }
}
.orders__number {
  padding: 2rem;
  background-color: #000;
  color: #fff;
}
.orders__track {
  padding: 1rem;
  background-color: #575fcf;
  color: #fff;
}
.orders__track:hover {
  background-color: #3c40c6;
}
.orders__details-wrapper {
  padding: 2.4rem 3.2rem;
  border: 3px solid #000;
}
@media screen and (max-width: 37.5em) {
  .orders__details-wrapper {
    border: unset;
  }
}
.orders__title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2.4rem;
}
@media screen and (max-width: 31.25em) {
  .orders__title-wrapper {
    align-items: flex-start;
    flex-direction: column;
  }
}
.orders__item {
  display: grid;
  grid-template-columns: 11.2rem 1fr;
}
.orders__item:not(:last-child) {
  margin-bottom: 1.2rem;
}
@media screen and (max-width: 31.25em) {
  .orders__item {
    grid-template-columns: 1fr;
  }
}
.orders__img {
  width: 100%;
  height: 100%;
}
.orders__item-details {
  padding: 2rem;
  background-color: #fbfbfb;
}
.orders__item-name {
  margin-bottom: 1.2rem;
}
.orders__item-totals {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media screen and (max-width: 31.25em) {
  .orders__item-totals {
    align-items: flex-start;
    flex-direction: column;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 2rem 0;
}
.pagination__btn {
  padding: 0.8rem 1.2rem;
  border: 2px solid #000;
  background-color: #fff;
  color: #000;
  cursor: pointer;
  transition: all 0.3s ease;
}
.pagination__btn:hover {
  background-color: #000;
  color: #fff;
}
.pagination__btn--active {
  background-color: #575fcf;
  border-color: #575fcf;
  color: #fff;
}
.pagination__btn--active:hover {
  background-color: #3c40c6;
  border-color: #3c40c6;
}
.pagination__btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.pagination__btn:disabled:hover {
  background-color: #fff;
  color: #000;
}

.profile {
  padding: 4rem 2.4rem;
}
.profile__container {
  display: grid;
  grid-template-columns: 24rem 1fr;
  grid-gap: 2rem;
}
@media screen and (max-width: 54.375em) {
  .profile__container {
    grid-template-columns: 1fr;
  }
}
@media screen and (max-width: 54.375em) {
  .profile__nav {
    border-bottom: 2px solid #000;
  }
}
@media screen and (max-width: 38.75em) {
  .profile__nav {
    padding-bottom: 2rem;
  }
}
.profile__nav-title {
  margin-bottom: 2rem;
}
@media screen and (max-width: 54.375em) {
  .profile__nav-list {
    display: grid;
    grid-template-columns: repeat(4, max-content);
    grid-gap: 1.2rem;
  }
}
@media screen and (max-width: 38.75em) {
  .profile__nav-list {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 25em) {
  .profile__nav-list {
    grid-template-columns: 1fr;
  }
}
.profile__nav-list-item:not(:last-child) {
  margin-bottom: 1.2rem;
}
@media screen and (max-width: 38.75em) {
  .profile__nav-list-item:not(:last-child) {
    margin-bottom: unset;
  }
}
.profile__nav-link {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  color: #000;
}
.profile__nav-link--active {
  font-weight: 700;
}
@media screen and (max-width: 54.375em) {
  .profile__nav-link {
    background-color: #f6f6f6;
  }
}
.profile__nav-link:hover {
  background-color: #f6f6f6;
}
.profile__nav-icon {
  width: 2.4rem;
  height: 2.4rem;
  object-fit: contain;
}
.address {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
}
@media screen and (max-width: 37.5em) {
  .address {
    grid-template-columns: 1fr;
  }
}
.address__single {
  border: 2px solid #f6f6f6;
}
.address__content {
  padding: 2rem;
}
.address__interaction-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}
.address__btn {
  padding: 1rem 2rem;
  background-color: transparent;
  cursor: pointer;
}
.address__btn--edit {
  color: #575fcf;
  border: 2px solid #575fcf;
}
.address__btn--edit:hover {
  background-color: #575fcf;
  color: #fff;
}
.address__btn--delete {
  color: red;
  border: 2px solid red;
}
.address__btn--delete:hover {
  background-color: red;
  color: #fff;
}

.checkout {
  padding: 8rem 2.4rem;
  background-color: #f6f6f6;
}
.checkout__grid {
  display: grid;
  grid-template-columns: 1.6fr 1fr;
  align-items: start;
  grid-gap: 3.2rem;
}
@media screen and (max-width: 58.125em) {
  .checkout__grid {
    grid-template-columns: 1fr;
  }
}
.checkout__form {
  padding: 3.6rem;
  background-color: #fff;
}
@media screen and (max-width: 58.125em) {
  .checkout__form {
    grid-row: 2/3;
  }
}
.checkout__title {
  margin-bottom: 1.2rem;
}
.checkout__section {
  padding-bottom: 2rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #575fcf;
}
.checkout__addresses .cart__address {
  background-color: #fbfbfb;
}
.checkout__addresses .cart__address--selected {
  background-color: #575fcf;
}
.checkout__addresses .cart__address:not(:last-child) {
  margin-bottom: 1.6rem;
}
.checkout__input-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 1.2rem;
}
@media screen and (max-width: 65.5em) {
  .checkout__input-grid {
    grid-template-columns: 1fr;
    grid-gap: 2.4rem;
  }
}
.checkout__input-triple {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.2rem;
}
@media screen and (max-width: 65.5em) {
  .checkout__input-triple {
    grid-template-columns: 1fr;
    grid-gap: 2.4rem;
  }
}

.success {
  padding: 16rem 2.4rem 8rem;
  background-color: #fbfbfb;
}
.success__container {
  max-width: 80rem;
  margin: 0 auto;
  background-color: #fff;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.success__img-wrapper {
  height: 30rem;
}
.success__img {
  display: block;
  width: 100%;
  height: 100%;
}
.success__content-wrapper {
  padding: 3.2rem;
}
.success__section {
  padding: 1rem 0;
  margin: 2rem 0;
  border-top: 2px solid #cacaca;
  border-bottom: 2px solid #cacaca;
}
.about {
  padding: 6rem 2.4rem;
}

.search-header {
  padding: 2rem 0;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}
.search-header__content {
  text-align: center;
}
.search-header__title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1.5rem;
}
@media (max-width: 768px) {
  .search-header__title {
    font-size: 2rem;
  }
}
.search-header__form {
  max-width: 600px;
  margin: 0 auto 1rem;
}
.search-header__query {
  color: #666;
  font-size: 1.1rem;
}
.search-header__query strong {
  color: #333;
}

.search-form {
  display: flex;
  background: white;
  border: 2px solid #ddd;
  border-radius: 50px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.search-form:focus-within {
  border-color: #007bff;
  box-shadow: 0 2px 20px rgba(0, 123, 255, 0.2);
}
.search-form__input {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  outline: none;
  font-size: 1.1rem;
  background: transparent;
}
.search-form__input::placeholder {
  color: #999;
}
.search-form__btn {
  padding: 1rem 1.5rem;
  background: #007bff;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-form__btn:hover {
  background: #0056b3;
}
.search-form__btn img {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1);
}

.search-results {
  padding: 2rem 0;
}

.search-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}
@media (max-width: 768px) {
  .search-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}
.search-controls__left {
  flex: 1;
}
.search-controls__right {
  display: flex;
  gap: 1rem;
}
@media (max-width: 768px) {
  .search-controls__right {
    flex-direction: column;
    gap: 0.5rem;
  }
}
.search-controls__count {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}
.search-controls__group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.search-controls__group label {
  font-weight: 500;
  color: #555;
  white-space: nowrap;
}
.search-controls__select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 0.9rem;
  min-width: 120px;
}
.search-controls__select:focus {
  outline: none;
  border-color: #007bff;
}

.no-results,
.no-query,
.error {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}
.no-results h3,
.no-query h3,
.error h3 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: #333;
}
.no-results p,
.no-query p,
.error p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}
.no-results .btn,
.no-query .btn,
.error .btn {
  display: inline-block;
  padding: 0.75rem 2rem;
  background: #007bff;
  color: white;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}
.no-results .btn:hover,
.no-query .btn:hover,
.error .btn:hover {
  background: #0056b3;
  transform: translateY(-2px);
}

.error h3 {
  color: #dc3545;
}
.error .btn {
  background: #dc3545;
}
.error .btn:hover {
  background: #c82333;
}

.search-suggestions {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
}

.search-suggestion {
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}
.search-suggestion:hover {
  background-color: #f8f9fa;
}
.search-suggestion:last-child {
  border-bottom: none;
}
.search-suggestion__name {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.25rem;
}
.search-suggestion__category {
  font-size: 0.85rem;
  color: #666;
}

.loading {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
  font-size: 1.1rem;
}
.loading::after {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 0.5rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.products__card .products__card-category {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 0.5rem;
  font-style: italic;
}

@media (max-width: 768px) {
  .search-header {
    padding: 1.5rem 0;
  }
  .search-header__title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }
  .search-form__input {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
  .search-form__btn {
    padding: 0.75rem 1rem;
  }
  .search-form__btn img {
    width: 18px;
    height: 18px;
  }
  .search-controls {
    padding: 0.75rem;
  }
  .search-controls__count {
    font-size: 1rem;
  }
  .search-controls__select {
    font-size: 0.85rem;
    min-width: 100px;
  }
}

/*# sourceMappingURL=main.css.map */
