@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap");
body {
  font-family: "Roboto", sans-serif;
  font-size: 2rem;
  line-height: 1.5;
}

.xxxl {
  font-size: clamp(8rem, 10vw + 1rem, 12rem);
  line-height: 1.3;
}

.xxl {
  font-size: clamp(6rem, 8vw + 1rem, 8rem);
  line-height: 1.3;
}

.xl {
  font-size: clamp(4.8rem, 6vw + 1rem, 6rem);
  line-height: 1.3;
}

.l {
  font-size: clamp(3.6rem, 4.8vw + 1rem, 4.8rem);
  line-height: 1.3;
}

.m {
  font-size: clamp(2.8rem, 3.6vw + 1rem, 3.6rem);
  line-height: 1.5;
}

.s {
  font-size: 2.4rem;
  line-height: 1.7;
}

.copy {
  font-size: 2rem;
  line-height: 1.5;
}

*,
*::after,
*::before {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
}

html {
  font-size: 62.5%;
}
@media screen and (max-width: 64em) {
  html {
    font-size: 56.25%;
  }
}
@media screen and (max-width: 48em) {
  html {
    font-size: 50%;
  }
}

body {
  box-sizing: border-box;
  background-color: #fff;
  min-height: 100vh;
  background-color: #f6f6f6;
  overflow-x: hidden;
}

::selection {
  background-color: #575fcf;
  color: #fff;
}

a {
  text-decoration: none;
}

a,
button {
  display: inline-block;
}

button,
input,
textarea {
  outline: none;
  border: none;
  font-family: inherit;
  font-size: inherit;
}

ol,
ul {
  list-style: none;
}

img {
  display: block;
  max-width: 100%;
  border: 0;
  object-fit: cover;
}

.bold {
  font-weight: 700;
}

.normal {
  font-weight: 400;
}

.light {
  font-weight: 300;
}

.text-muted {
  color: #ababab;
}

.text-warning {
  color: #f39c12;
}

.container {
  width: 100%;
  max-width: 127.8rem;
  margin: 0 auto;
}

.btn {
  display: block;
  position: relative;
  z-index: 1;
  padding: 1.6rem 3rem;
  text-align: center;
  font-weight: 700;
  overflow: hidden;
  cursor: pointer;
}
.btn--primary {
  background-color: #575fcf;
  color: #fff;
}
.btn--primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-color: #3c40c6;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.2s ease-in-out;
}
.btn--primary:hover::before {
  transform: scaleX(1);
}
.btn--full {
  width: 100%;
}
.btn--ghost {
  height: 100%;
  padding: 2rem;
  background-color: transparent;
  border: 2px solid #575fcf;
  color: #575fcf;
  transition: background-color 0.3s;
}
.btn--ghost:hover {
  background-color: #575fcf;
  color: #fff;
}
.btn--trash {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 5.5rem;
  height: 5.5rem;
  padding: 1rem;
  background-color: transparent;
  border: 2px solid red;
  transition: background-color 0.3s, border-color 0.3s;
}
.btn--trash:hover {
  border-color: #cc0000;
  background-color: #cc0000;
}
.btn--trash .btn__icon {
  width: 2.4rem;
  height: 2.4rem;
}
.btn .btn:disabled {
  opacity: 0.3;
  pointer-events: none;
}

.input:not(:last-child) {
  margin-bottom: 2.4rem;
}
.input--radio:not(:last-child) {
  margin-bottom: 0.4rem;
}
.input--radio .input__label {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.8rem;
}
.input--radio .input__control {
  width: max-content;
}
.input__label {
  display: block;
}
.input__control {
  display: block;
  width: 100%;
  padding: 2rem;
  background-color: #f6f6fe;
  border: 1px solid #ababab;
  font-size: inherit;
}
.input__control--textarea {
  resize: vertical;
  min-height: 20rem;
}
.input__control--error + .input__error {
  display: block;
}
.input__error {
  display: none;
  color: red;
}

.link {
  background-color: transparent;
  color: #575fcf;
  cursor: pointer;
}
.link:hover {
  color: #3c40c6;
}

.toast {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 4;
  max-width: 800px;
  padding: 10px 20px;
  margin: auto;
  background: #cccccc;
  font-family: inherit;
  text-align: center;
  color: #333333;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s, top 0.2s, visibility 0.2s;
  border-radius: 3px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}
.toast--visible {
  bottom: 20px;
  opacity: 1;
  visibility: visible;
}
.toast--success {
  background: #00c02b;
  border-color: #009d23;
  color: #ffffff;
}
.toast--error {
  background: #d50000;
  border-color: #ba0000;
  color: #ffffff;
}

.nav {
  padding: 1.8rem 3.2rem 8rem;
  min-width: 32rem;
  background-color: #fff;
  height: 100%;
}
@media screen and (max-width: 50em) {
  .nav {
    padding: 2.4rem;
  }
}
@media screen and (max-width: 20em) {
  .nav {
    padding: 1rem;
  }
}
.nav__logo-wrapper {
  margin-bottom: 3.2rem;
}
.nav__logo {
  max-width: 10rem;
}
@media screen and (max-width: 50em) {
  .nav__contents {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 2rem;
  }
}
@media screen and (max-width: 33.75em) {
  .nav__contents {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 20em) {
  .nav__contents {
    grid-template-columns: 1fr;
    grid-gap: 1rem;
  }
}
.nav__link-wrapper:not(:last-child) {
  margin-bottom: 3.2rem;
}
@media screen and (max-width: 50em) {
  .nav__link-wrapper {
    padding: 2rem;
    background-color: #f6f6f6;
  }
  .nav__link-wrapper:not(:last-child) {
    margin-bottom: unset;
  }
}
.nav__link {
  color: #000;
}
.nav__link--active {
  color: #575fcf;
  font-weight: 700;
}
.nav__link:hover {
  color: #575fcf;
}
.nav__link-list {
  margin-bottom: 1.2rem;
}
.nav__link-list-item:not(:last-child) {
  margin-bottom: 0.4rem;
}

.nav-up {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  padding: 2rem 2.4rem;
  background-color: #fff;
}
@media screen and (max-width: 37.5em) {
  .nav-up {
    flex-direction: column;
    align-items: flex-start;
  }
}
.nav-up__left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 3.2rem;
}
@media screen and (max-width: 37.5em) {
  .nav-up__left {
    align-items: stretch;
    width: 100%;
  }
}
.nav-up__search-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1.2rem;
  background-color: #f6f6f6;
  padding: 0.5rem 2rem;
}
@media screen and (max-width: 37.5em) {
  .nav-up__search-wrapper {
    width: 100%;
  }
}
.nav-up__input-control {
  padding: 1rem 0;
  background-color: transparent;
  min-width: 32rem;
}
@media screen and (max-width: 73.125em) {
  .nav-up__input-control {
    min-width: unset;
    width: 100%;
  }
}
.nav-up__right {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 2rem;
}
@media screen and (max-width: 37.5em) {
  .nav-up__right {
    justify-content: flex-end;
    width: 100%;
  }
}
.nav-up__user {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.8rem;
  color: #000;
}
.nav-up__user:hover {
  color: #575fcf;
}
.nav-up__user:hover .nav-up__user-icon-wrapper {
  background-color: #575fcf;
}
.nav-up__user-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 5rem;
  height: 5rem;
  padding: 1rem;
  background-color: #f6f6fe;
  border-radius: 50%;
  transition: background-color 0.3s ease-in-out;
}
.footer {
  margin-top: 1.6rem;
  padding: 2rem;
  background-color: #fff;
  text-align: center;
}

.grid {
  display: grid;
  grid-template-columns: max-content 1fr;
  grid-gap: 1.6rem;
  padding-right: 2rem;
}
@media screen and (max-width: 50em) {
  .grid {
    grid-template-columns: 1fr;
    padding-right: unset;
  }
}
@media screen and (max-width: 50em) {
  .grid__main {
    padding: 1rem;
  }
}
.grid__breadcrum {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  padding: 2rem 0;
}
.grid__breadcrum-item:not(:last-child)::after {
  content: ">";
}
.grid__breadcrum-link {
  color: #575fcf;
  text-decoration: underline;
}
.grid__breadcrum-link:hover {
  color: #3c40c6;
}

.orders__single {
  display: grid;
  grid-template-columns: max-content 1fr;
}
@media screen and (max-width: 37.5em) {
  .orders__single {
    grid-template-columns: 1fr;
    border: 3px solid #000;
  }
}
.orders__single:not(:last-child) {
  margin-bottom: 3.2rem;
}
.orders__preview-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
@media screen and (max-width: 37.5em) {
  .orders__preview-wrapper {
    gap: 0;
  }
}
.orders__number {
  padding: 2rem;
  background-color: #000;
  color: #fff;
}
.orders__track {
  padding: 1rem;
  background-color: #575fcf;
  color: #fff;
}
.orders__track:hover {
  background-color: #3c40c6;
}
.orders__details-wrapper {
  padding: 2.4rem 3.2rem;
  border: 3px solid #000;
  background-color: #fff;
}
@media screen and (max-width: 37.5em) {
  .orders__details-wrapper {
    border: unset;
  }
}
.orders__title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2.4rem;
}
@media screen and (max-width: 31.25em) {
  .orders__title-wrapper {
    align-items: flex-start;
    flex-direction: column;
  }
}
.orders__item {
  display: grid;
  grid-template-columns: 11.2rem 1fr;
}
.orders__item:not(:last-child) {
  margin-bottom: 1.2rem;
}
@media screen and (max-width: 31.25em) {
  .orders__item {
    grid-template-columns: 1fr;
  }
}
.orders__img {
  width: 100%;
  height: 100%;
}
.orders__item-details {
  padding: 2rem;
  background-color: #f6f6fe;
}
.orders__item-name {
  margin-bottom: 1.2rem;
}
.orders__item-totals {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media screen and (max-width: 31.25em) {
  .orders__item-totals {
    align-items: flex-start;
    flex-direction: column;
  }
}

.cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2.4rem;
}
@media screen and (max-width: 65em) {
  .cards {
    grid-template-columns: 1fr;
  }
}
.cards__single {
  padding: 2rem;
  border-left: 1.2rem solid #575fcf;
  border: 2px solid #575fcf;
  border-left: 2rem solid #575fcf;
}
.cards__single--link {
  color: #000;
  transition: background-color 0.3s ease, color 0.3s ease;
}
.cards__single--link:hover {
  background-color: #575fcf;
  color: #fff;
  border-left-color: #3c40c6;
}
.cards__title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100vw;
  height: 100vh;
  padding: 2rem;
  display: none;
}
.modal--visible {
  display: block;
}
.modal__bg {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
}
.modal__container {
  position: relative;
  z-index: 3;
  max-width: 110rem;
  margin: 3rem auto;
  padding: 3rem;
  background-color: #fff;
  border-radius: 8px;
}
.modal__close-wrapper {
  text-align: right;
}
.modal__btn {
  font-size: 3rem;
  padding: 1rem 2rem 1.6rem;
  border-radius: 4px;
  background-color: #575fcf;
  color: #fff;
  line-height: 1;
  cursor: pointer;
}
.modal__btn:hover {
  color: #fff;
  background-color: #3c40c6;
}
.modal__header {
  margin-bottom: 1.6rem;
}
.modal__details {
  margin-bottom: 1.6rem;
}
.modal__input-wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
}
@media screen and (max-width: 32.5em) {
  .modal__input-wrapper {
    grid-template-columns: 1fr;
    margin-bottom: 3rem;
    grid-gap: 0;
  }
}
.modal__btn-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.dashboard__cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
  max-width: 80rem;
}
.dashboard__card {
  padding: 1rem 2rem 3.2rem;
  background-color: #fff;
  text-align: center;
}
.dashboard__status {
  text-align: right;
}
.dashboard__status--positive {
  color: green;
}
.dashboard__status--negative {
  color: red;
}
.dashboard__charts {
  padding: 2rem;
  width: 100%;
  max-width: none;
}
.dashboard__products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.6rem;
  margin-top: 2rem;
  width: 100%;
}
@media screen and (max-width: 70em) {
  .dashboard__products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 32.5em) {
  .dashboard__products-grid {
    grid-template-columns: 1fr;
  }
}
.dashboard__products-grid .product-card {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.12);
  width: 100%;
}
.dashboard__section {
  margin-bottom: 3rem;
  width: 100%;
}
.dashboard__section h3 {
  margin-bottom: 1rem;
  color: #575fcf;
}
.dashboard__section .loading-message {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-style: italic;
  grid-column: 1/-1;
}

.login {
  display: grid;
  grid-template-columns: 1.3fr 1fr;
  grid-gap: 2rem;
  width: 100vw;
  height: 100vh;
}
@media screen and (max-width: 71.25em) {
  .login {
    grid-template-columns: 1fr 1.2fr;
  }
}
@media screen and (max-width: 51.875em) {
  .login {
    grid-template-columns: 1fr;
  }
}
.login__img-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.login__logo {
  width: 100%;
  height: auto;
  max-width: 60rem;
}
@media screen and (max-width: 51.875em) {
  .login__logo {
    width: auto;
    height: 30rem;
  }
}
.login__form-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  height: 100%;
  padding: 16rem 8rem 4rem;
  background-color: #fff;
  box-shadow: -10px 0 20px rgba(0, 0, 0, 0.1);
}
@media screen and (max-width: 31.25em) {
  .login__form-wrapper {
    padding: 6rem 3.2rem 4rem;
  }
}
.login__form {
  width: 100%;
  margin: 2rem 0;
}
.login__link-wrapper {
  margin-bottom: 2rem;
}
.login__copyright {
  margin-top: auto;
}

.register__nav {
  padding: 2rem;
}
.register__nav-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.register__nav-logo {
  width: 10rem;
}
.register__header {
  margin: 2rem 2rem 5rem;
}
.register__form {
  padding: 0 2rem 4rem;
}
.register__section {
  display: grid;
  grid-template-columns: 20rem 1fr;
  grid-gap: 3.2rem;
}
.register__section:not(:last-child) {
  margin-bottom: 4rem;
  padding-bottom: 4rem;
  border-bottom: 2px solid #ababab;
}
@media screen and (max-width: 56.875em) {
  .register__section {
    grid-template-columns: 1fr;
  }
}
.register__controls-wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
}
@media screen and (max-width: 30.625em) {
  .register__controls-wrapper {
    grid-template-columns: 1fr;
  }
}
.register__input--full {
  grid-column: 1/-1;
}
.register__btn-wrapper {
  padding: 0 2rem;
}
.register__btn-wrapper-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.success {
  margin: 6rem 2rem;
}
.success__grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  grid-gap: 2rem;
}
@media screen and (max-width: 43.75em) {
  .success__grid {
    grid-template-columns: 1fr;
    text-align: center;
  }
}
@media screen and (max-width: 43.75em) {
  .success__img {
    width: 100%;
    height: auto;
  }
}
.success__content-wrapper {
  max-width: 44rem;
}
@media screen and (max-width: 43.75em) {
  .success__content-wrapper {
    max-width: unset;
  }
}

.product__section {
  padding: 3.6rem;
  background-color: #fff;
}
.product__section:not(:last-child) {
  margin-bottom: 2.4rem;
}
.product__section-title {
  margin-bottom: 2rem;
}
.product__addon-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
}
.product__addon-wrapper:not(:last-child) {
  margin-bottom: 1.6rem;
}
.product__addon-wrapper .input {
  min-width: 40rem;
}
.product__addon-wrapper .btn {
  margin-top: 0.8rem;
}
.product__images-wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
}
@media screen and (max-width: 28.75em) {
  .product__images-wrapper {
    grid-template-columns: 1fr;
  }
}
.product__img-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  grid-gap: 1.6rem;
}
.product__thumbnail {
  width: 100%;
  height: 100%;
  max-width: 12rem;
  max-height: 7rem;
}
.product__btn-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  margin-top: 3rem;
  padding-top: 3rem;
  border-top: 2px solid #575fcf;
}
.product__btns-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  margin-top: 3rem;
  padding-top: 3rem;
  border-top: 2px solid #575fcf;
}
.product__cards-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.2rem;
}
@media screen and (max-width: 70em) {
  .product__cards-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 70em) {
  .product__cards-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 32.5em) {
  .product__cards-wrapper {
    grid-template-columns: 1fr;
  }
}

.product-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}
.product-card__img-wrapper {
  position: relative;
  height: 25rem;
  overflow: hidden;
}
.product-card__img-link-wrapper {
  display: block;
  width: 100%;
  height: 100%;
}
.product-card__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}
.product-card__img:hover {
  transform: scale(1.05);
}
.product-card__content {
  padding: 1.5rem;
  background-color: #fff;
}
.product-card__content .btn {
  margin-top: 1rem;
}
.product-card__link {
  color: #000;
  text-decoration: none;
  font-size: 1.1rem;
  line-height: 1.4;
  display: block;
  margin-bottom: 0.8rem;
}
.product-card__link:hover {
  color: #575fcf;
}
.product-card__price {
  margin-bottom: 0.8rem;
  font-size: 1.1rem;
  font-weight: bold;
}
.product-card__price--discounted {
  color: #e74c3c;
  font-weight: bold;
}
.product-card__price--original {
  color: #999;
  text-decoration: line-through;
  margin-left: 0.5rem;
  font-size: 0.9rem;
  font-weight: normal;
}
.product-card__discount {
  background: #e74c3c;
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-left: 0.5rem;
  font-weight: normal;
}
.product-card__stock {
  font-size: 0.9rem;
  color: #666;
}
.product-card__stock .product-stock--low {
  color: #e74c3c;
  font-weight: bold;
}
.product-card__category {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.product-card__discount-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #e74c3c;
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  z-index: 2;
}
.product-card__original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 0.9rem;
  margin-right: 0.5rem;
}
.product-card__final-price {
  color: #e74c3c;
  font-weight: bold;
  font-size: 1.1rem;
}

.discounts__wrapper {
  padding: 2.4rem;
  background-color: #fff;
}
.discounts__wrapper:not(:last-child) {
  margin-bottom: 2.4rem;
}
.discounts__header {
  margin-bottom: 2.4rem;
}
.discounts__form-wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
}
@media screen and (max-width: 29.375em) {
  .discounts__form-wrapper {
    grid-template-columns: 1fr;
  }
}
.discounts__form-wrapper .input:not(:last-child) {
  margin-bottom: 0;
}
.discounts__btn-wrapper {
  grid-column: 1/-1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.coupon-status {
  padding: 0.4rem 0.8rem;
  border-radius: 0.4rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: uppercase;
}
.coupon-status.active {
  background-color: #d4edda;
  color: #155724;
}
.coupon-status.inactive {
  background-color: #f8d7da;
  color: #721c24;
}
.coupon-status.expired {
  background-color: #d1ecf1;
  color: #0c5460;
}

.loading-container, .empty-container {
  padding: 4rem 2rem;
  text-align: center;
}
.loading-container .loading-message, .loading-container .empty-message, .empty-container .loading-message, .empty-container .empty-message {
  color: #ababab;
}
.loading-container .loading-message h3, .loading-container .empty-message h3, .empty-container .loading-message h3, .empty-container .empty-message h3 {
  margin-bottom: 1rem;
  color: #000;
}
.loading-container .loading-message p, .loading-container .empty-message p, .empty-container .loading-message p, .empty-container .empty-message p {
  margin: 0;
}

.users__single {
  margin-bottom: 3.2rem;
}
.users__grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 1.2rem;
  max-width: 80rem;
}
@media screen and (max-width: 31.875em) {
  .users__grid {
    grid-template-columns: 1fr;
  }
}
.users__grid-item {
  padding: 3.2rem;
  background-color: #fff;
}
.users__update {
  margin-top: 2rem;
  padding: 3.2rem;
  max-width: 80rem;
  background-color: #fff;
}
.users__input-wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 1.2rem;
}
@media screen and (max-width: 31.875em) {
  .users__input-wrapper {
    grid-template-columns: 1fr;
  }
}
.users__btn-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
@media screen and (max-width: 31.875em) {
  .users__btn-wrapper {
    margin-top: 2rem;
  }
}

.payment {
  padding: 2rem;
  background-color: #fff;
}
.payment__intro {
  margin-bottom: 3rem;
}
.payment__table-single {
  display: grid;
  grid-template-columns: repeat(3, 24rem);
  grid-gap: 1rem;
}
@media screen and (max-width: 71.875em) {
  .payment__table-single {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media screen and (max-width: 30em) {
  .payment__table-single {
    grid-template-columns: 1fr;
  }
}
.payment__table-single:not(:last-child) {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid rgba(171, 171, 171, 0.6);
}
.payment__info--pending {
  color: red;
}
.payment__info--settled {
  color: green;
}

/*# sourceMappingURL=main.css.map */
