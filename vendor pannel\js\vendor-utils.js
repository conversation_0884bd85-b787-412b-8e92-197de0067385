// Universal Vendor Panel Utilities
// This file provides common functionality for all vendor panel pages

class VendorUtils {
  constructor() {
    this.apiBaseUrl = 'http://localhost:5000/api';
    this.token = localStorage.getItem('skeyy_auth_token');
    this.vendorData = null;
    this.init();
  }

  init() {
    // Check authentication on page load
    this.checkAuthentication();
    
    // Setup logout functionality
    this.setupLogout();
    
    // Load and display vendor info
    this.loadVendorInfo();
    
    // Setup page-specific functionality
    this.setupPageSpecific();
  }

  checkAuthentication() {
    if (!this.token) {
      alert('You need to login first to access the vendor panel.');
      window.location.href = '../frontend/login.html';
      return false;
    }
    return true;
  }

  setupLogout() {
    // Find all logout links and buttons
    const logoutElements = document.querySelectorAll('a[href="logout.html"], .logout-btn, [data-action="logout"]');
    
    logoutElements.forEach(element => {
      element.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleLogout();
      });
    });
  }

  handleLogout() {
    // Show confirmation dialog
    if (confirm('Are you sure you want to logout? You will be redirected to the main login page.')) {
      this.performLogout();
    }
  }

  async performLogout() {
    try {
      // Show logout message
      this.showMessage('Logging out... Redirecting to main login page.', 'info');

      // Call logout API
      await this.makeApiCall('/auth/logout', { method: 'POST' });
      
    } catch (error) {
      console.error('Logout API error:', error);
      // Continue with logout even if API call fails
    } finally {
      // Clear authentication data
      this.clearAuthData();
      
      // Redirect after short delay to show message
      setTimeout(() => {
        window.location.href = '../frontend/login.html';
      }, 1000);
    }
  }

  clearAuthData() {
    localStorage.removeItem('skeyy_auth_token');
    localStorage.removeItem('skeyy_user_data');
    localStorage.removeItem('skeyy_vendor_profile');
  }

  async loadVendorInfo() {
    try {
      // First try to get basic user info from token
      const userData = this.getUserFromToken();
      if (userData) {
        this.updateVendorDisplay(userData.email.split('@')[0]);
      }

      // Then try to load full vendor profile for business name
      const profileResponse = await this.makeApiCall('/vendor/profile');
      if (profileResponse.success) {
        this.vendorData = profileResponse.data;
        const profile = profileResponse.data.vendor.profile;
        
        // Update display with business name if available
        const displayName = profile?.business_name || profile?.contact_name || userData?.email?.split('@')[0] || 'Vendor';
        this.updateVendorDisplay(displayName);
        
        // Update page title if applicable
        this.updatePageTitle(displayName);
        
        // Store vendor data for other components
        localStorage.setItem('skeyy_vendor_profile', JSON.stringify(profileResponse.data));
      }
      
    } catch (error) {
      console.error('Error loading vendor info:', error);
      // Fallback to basic display
      this.updateVendorDisplay('Vendor');
    }
  }

  getUserFromToken() {
    try {
      const userData = localStorage.getItem('skeyy_user_data');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }

  updateVendorDisplay(displayName) {
    // Update all vendor name elements
    const vendorNameElements = document.querySelectorAll('.nav-up__user');
    vendorNameElements.forEach(element => {
      const iconWrapper = element.querySelector('.nav-up__user-icon-wrapper');
      if (iconWrapper) {
        // Keep the icon but update the text
        element.innerHTML = '';
        element.appendChild(iconWrapper);
        element.appendChild(document.createTextNode(displayName));
      } else {
        // If no icon wrapper, just update text content
        element.textContent = displayName;
      }
    });

    // Update any other vendor display elements
    const vendorDisplayElements = document.querySelectorAll('.vendor-name, .business-name');
    vendorDisplayElements.forEach(element => {
      element.textContent = displayName;
    });
  }

  updatePageTitle(businessName) {
    // Update page title elements
    const pageTitleElements = document.querySelectorAll('.nav-up__current-page');
    pageTitleElements.forEach(element => {
      const currentText = element.textContent;
      if (currentText === 'Dashboard') {
        element.textContent = `${businessName} - Dashboard`;
      }
      // For other pages, we can add similar logic if needed
    });

    // Update browser title if on dashboard
    if (document.title.includes('Dashboard') || window.location.pathname.includes('index.html')) {
      document.title = `${businessName} - Vendor Dashboard | Skeyy`;
    }
  }

  setupPageSpecific() {
    // Setup page-specific functionality based on current page
    const currentPage = this.getCurrentPage();
    
    switch (currentPage) {
      case 'dashboard':
        this.setupDashboardSpecific();
        break;
      case 'profile':
        this.setupProfileSpecific();
        break;
      case 'products':
        this.setupProductsSpecific();
        break;
      case 'orders':
        this.setupOrdersSpecific();
        break;
      case 'coupons':
        this.setupCouponsSpecific();
        break;
      default:
        // Common setup for all pages
        break;
    }
  }

  getCurrentPage() {
    const path = window.location.pathname;
    if (path.includes('index.html') || path.endsWith('/')) return 'dashboard';
    if (path.includes('profile.html')) return 'profile';
    if (path.includes('product')) return 'products';
    if (path.includes('order')) return 'orders';
    if (path.includes('discount') || path.includes('coupon')) return 'coupons';
    return 'other';
  }

  setupDashboardSpecific() {
    // Dashboard-specific setup
    console.log('Setting up dashboard-specific functionality');
  }

  setupProfileSpecific() {
    // Profile-specific setup
    console.log('Setting up profile-specific functionality');
  }

  setupProductsSpecific() {
    // Products-specific setup
    console.log('Setting up products-specific functionality');
  }

  setupOrdersSpecific() {
    // Orders-specific setup
    console.log('Setting up orders-specific functionality');
  }

  setupCouponsSpecific() {
    // Coupons-specific setup
    console.log('Setting up coupons-specific functionality');
  }

  async makeApiCall(endpoint, options = {}) {
    const url = `${this.apiBaseUrl}${endpoint}`;
    const config = {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      ...options
    };

    const response = await fetch(url, config);
    
    if (!response.ok) {
      if (response.status === 401) {
        // Token expired or invalid
        this.clearAuthData();
        window.location.href = '../frontend/login.html';
        return;
      }
      
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        // If we can't parse the error response, use the default message
      }
      
      throw new Error(errorMessage);
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || 'API call failed');
    }

    return data;
  }

  showMessage(message, type = 'info') {
    // Simple message display - can be enhanced with toast notifications
    console.log(`${type.toUpperCase()}: ${message}`);
    
    // You can implement a more sophisticated notification system here
    if (type === 'error') {
      alert(`Error: ${message}`);
    } else if (type === 'success') {
      // For success messages, we might want to show them differently
      console.log(`Success: ${message}`);
    } else {
      // For info messages
      console.log(`Info: ${message}`);
    }
  }

  // Utility method to get vendor data
  getVendorData() {
    return this.vendorData;
  }

  // Utility method to get business name
  getBusinessName() {
    const profile = this.vendorData?.vendor?.profile;
    return profile?.business_name || profile?.contact_name || 'Vendor';
  }
}

// Initialize VendorUtils when the page loads
let vendorUtils;
document.addEventListener('DOMContentLoaded', () => {
  vendorUtils = new VendorUtils();
});

// Export for use in other scripts
window.VendorUtils = VendorUtils;
