.product {
  &__section {
    padding: 3.6rem;
    background-color: $color-white;

    &:not(:last-child) {
      margin-bottom: 2.4rem;
    }
  }

  &__section-title {
    margin-bottom: 2rem;
  }

  &__addon-wrapper {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 1rem;

    &:not(:last-child) {
      margin-bottom: 1.6rem;
    }

    .input {
      min-width: 40rem;
    }

    .btn {
      margin-top: 0.8rem;
    }
  }

  &__images-wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 2rem;

    @include respond(460px) {
      grid-template-columns: 1fr;
    }
  }

  &__img-wrapper {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    grid-gap: 1.6rem;
  }

  &__thumbnail {
    width: 100%;
    height: 100%;
    max-width: 12rem;
    max-height: 7rem;
  }

  &__btn-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;

    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 2px solid $color-primary;
  }

  &__btns-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1rem;

    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 2px solid $color-primary;
  }

  &__cards-wrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 1.2rem;

    @include respond(1120px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @include respond(1120px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @include respond(520px) {
      grid-template-columns: 1fr;
    }
  }
}

.product-card {
  background-color: $color-white;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba($color-black, 0.15);
  }

  &__img-wrapper {
    position: relative;
    height: 25rem;
    overflow: hidden;
  }

  &__img-link-wrapper {
    display: block;
    width: 100%;
    height: 100%;
  }

  &__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  &__content {
    padding: 1.5rem;
    background-color: $color-white;

    .btn {
      margin-top: 1rem;
    }
  }

  &__category {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__link {
    color: $color-black;
    text-decoration: none;
    font-size: 1.1rem;
    line-height: 1.4;
    display: block;
    margin-bottom: 0.8rem;

    &:hover {
      color: $color-primary;
    }
  }

  &__price {
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
    font-weight: bold;

    &--discounted {
      color: #e74c3c;
      font-weight: bold;
    }

    &--original {
      color: #999;
      text-decoration: line-through;
      margin-left: 0.5rem;
      font-size: 0.9rem;
      font-weight: normal;
    }
  }

  &__original-price {
    color: #999;
    text-decoration: line-through;
    margin-right: 0.5rem;
    font-size: 0.9rem;
    font-weight: normal;
  }

  &__final-price {
    color: #e74c3c;
    font-weight: bold;
  }

  &__discount {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: #e74c3c;
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 2;
  }

  &__stock {
    font-size: 0.9rem;
    color: #666;

    .product-stock {
      &--low {
        color: #e74c3c;
        font-weight: bold;
      }
    }
  }
}
