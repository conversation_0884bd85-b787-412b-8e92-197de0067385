// Vendor Coupon Management JavaScript
class VendorCouponManager {
  constructor() {
    this.apiBaseUrl = 'http://localhost:5000/api';
    this.token = localStorage.getItem('skeyy_auth_token');
    this.collections = [];
    this.products = [];
    this.coupons = [];
    this.init();
  }

  init() {
    // Check authentication
    this.checkAuthentication();
    
    // Load initial data
    this.loadCollections();
    this.loadProducts();
    this.loadCoupons();
    this.loadSidebarCounts();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Set minimum date to today
    this.setMinimumDate();
  }

  checkAuthentication() {
    if (!this.token) {
      alert('You need to login first to access the vendor panel.');
      window.location.href = '../frontend/login.html';
      return false;
    }
    return true;
  }

  setMinimumDate() {
    const dateInput = document.getElementById('js-date');
    if (dateInput) {
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      dateInput.min = tomorrow.toISOString().split('T')[0];
    }
  }

  setupEventListeners() {
    // Form submission
    const submitBtn = document.getElementById('js-discount-btn');
    if (submitBtn) {
      submitBtn.addEventListener('click', (e) => this.handleFormSubmit(e));
    }

    // Product type change
    const productTypeSelect = document.getElementById('js-type');
    if (productTypeSelect) {
      productTypeSelect.addEventListener('change', (e) => this.handleProductTypeChange(e));
    }

    // Coupon type change for validation hints
    const couponTypeSelect = document.getElementById('js-discount-type');
    if (couponTypeSelect) {
      couponTypeSelect.addEventListener('change', (e) => this.handleCouponTypeChange(e));
    }

    // Auto-uppercase coupon code
    const codeInput = document.getElementById('js-code');
    if (codeInput) {
      codeInput.addEventListener('input', (e) => {
        e.target.value = e.target.value.toUpperCase();
      });
    }
  }

  async loadCollections() {
    try {
      const response = await this.makeApiCall('/vendor/coupons/collections');
      this.collections = response.data || [];
      console.log('Loaded collections:', this.collections.length);
    } catch (error) {
      console.error('Error loading collections:', error);
      this.showError('Failed to load collections');
    }
  }

  async loadProducts() {
    try {
      const response = await this.makeApiCall('/vendor/coupons/products');
      this.products = response.data || [];
      console.log('Loaded products:', this.products.length);
    } catch (error) {
      console.error('Error loading products:', error);
      this.showError('Failed to load products');
    }
  }

  handleProductTypeChange(e) {
    const productType = e.target.value;
    const collectionSelect = document.getElementById('js-collection');
    const label = document.querySelector('label[for="js-collection"]');
    
    if (!collectionSelect || !label) return;

    // Clear previous options
    collectionSelect.innerHTML = '';
    
    if (productType === 'collection') {
      label.textContent = 'Collection';
      collectionSelect.disabled = false;
      
      // Add default option
      const defaultOption = document.createElement('option');
      defaultOption.value = '';
      defaultOption.textContent = 'Select Collection';
      collectionSelect.appendChild(defaultOption);
      
      // Add collections
      this.collections.forEach(collection => {
        const option = document.createElement('option');
        option.value = collection.id;
        option.textContent = collection.name;
        collectionSelect.appendChild(option);
      });
      
    } else if (productType === 'product') {
      label.textContent = 'Product';
      collectionSelect.disabled = false;
      
      // Add default option
      const defaultOption = document.createElement('option');
      defaultOption.value = '';
      defaultOption.textContent = 'Select Product';
      collectionSelect.appendChild(defaultOption);
      
      // Add products
      this.products.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = `${product.name} (₹${product.price})`;
        collectionSelect.appendChild(option);
      });
      
    } else {
      label.textContent = 'Collection / Product';
      collectionSelect.disabled = true;
      
      const defaultOption = document.createElement('option');
      defaultOption.value = '';
      defaultOption.textContent = 'Select Product Type First';
      collectionSelect.appendChild(defaultOption);
    }
  }

  handleCouponTypeChange(e) {
    const couponType = e.target.value;
    const discountInput = document.getElementById('js-discount');
    const discountLabel = document.querySelector('label[for="js-discount"]');
    
    if (!discountInput || !discountLabel) return;

    if (couponType === 'discount') {
      discountLabel.textContent = 'Discount Percentage (%)';
      discountInput.placeholder = 'e.g., 10, 25, 50';
      discountInput.max = '100';
      discountInput.min = '1';
      discountInput.step = '1';
    } else if (couponType === 'flat_off') {
      discountLabel.textContent = 'Flat Amount Off (₹)';
      discountInput.placeholder = 'e.g., 100, 500, 1000';
      discountInput.removeAttribute('max');
      discountInput.min = '1';
      discountInput.step = '0.01';
    } else {
      discountLabel.textContent = 'Discount % / Amount';
      discountInput.placeholder = 'Enter discount value';
      discountInput.removeAttribute('max');
      discountInput.min = '1';
      discountInput.step = '0.01';
    }
  }

  async handleFormSubmit(e) {
    e.preventDefault();
    
    try {
      this.showLoading();
      
      // Validate and collect form data
      const formData = this.validateAndCollectFormData();
      if (!formData) {
        this.hideLoading();
        return;
      }

      console.log('Creating coupon with data:', formData);

      // Create coupon
      const response = await this.makeApiCall('/vendor/coupons', {
        method: 'POST',
        body: JSON.stringify(formData)
      });

      this.showSuccess('Coupon created successfully!');
      this.resetForm();
      this.loadCoupons(); // Reload coupons list

    } catch (error) {
      console.error('Error creating coupon:', error);
      this.showError(error.message || 'Failed to create coupon');
    } finally {
      this.hideLoading();
    }
  }

  validateAndCollectFormData() {
    const productType = document.getElementById('js-type')?.value;
    const collectionId = document.getElementById('js-collection')?.value;
    const couponType = document.getElementById('js-discount-type')?.value;
    const discountValue = document.getElementById('js-discount')?.value;
    const code = document.getElementById('js-code')?.value;
    const expirationDate = document.getElementById('js-date')?.value;
    const usageLimit = document.getElementById('js-usage-limit')?.value;
    const minOrderAmount = document.getElementById('js-min-order')?.value;

    // Validation
    if (!productType) {
      this.showError('Please select a product type');
      return null;
    }

    if (!collectionId) {
      this.showError('Please select a collection or product');
      return null;
    }

    if (!couponType) {
      this.showError('Please select a coupon type');
      return null;
    }

    if (!discountValue || parseFloat(discountValue) <= 0) {
      this.showError('Please enter a valid discount value');
      return null;
    }

    if (couponType === 'discount' && parseFloat(discountValue) > 100) {
      this.showError('Discount percentage cannot exceed 100%');
      return null;
    }

    if (!code || code.length < 3) {
      this.showError('Please enter a coupon code (minimum 3 characters)');
      return null;
    }

    if (!expirationDate) {
      this.showError('Please select an expiration date');
      return null;
    }

    const expDate = new Date(expirationDate);
    if (expDate <= new Date()) {
      this.showError('Expiration date must be in the future');
      return null;
    }

    // Build form data
    const formData = {
      code: code.toUpperCase(),
      product_type: productType,
      coupon_type: couponType,
      discount_value: parseFloat(discountValue),
      expiration_date: expirationDate
    };

    // Add collection_id or product_id based on product_type
    if (productType === 'collection') {
      formData.collection_id = parseInt(collectionId);
    } else if (productType === 'product') {
      formData.product_id = parseInt(collectionId);
    }

    // Add optional fields
    if (usageLimit && parseInt(usageLimit) > 0) {
      formData.usage_limit = parseInt(usageLimit);
    }

    if (minOrderAmount && parseFloat(minOrderAmount) > 0) {
      formData.minimum_order_amount = parseFloat(minOrderAmount);
    }

    return formData;
  }

  resetForm() {
    const form = document.querySelector('.discounts__form-wrapper');
    if (form) {
      const inputs = form.querySelectorAll('input, select');
      inputs.forEach(input => {
        if (input.type === 'select-one') {
          input.selectedIndex = 0;
        } else {
          input.value = '';
        }
      });
      
      // Reset collection/product dropdown
      this.handleProductTypeChange({ target: { value: '' } });
      
      // Hide all error messages
      const errorSpans = form.querySelectorAll('.input__error');
      errorSpans.forEach(span => {
        span.style.display = 'none';
      });
    }
  }

  async loadCoupons() {
    try {
      this.showCouponsLoading();

      console.log('Loading coupons...');
      const response = await this.makeApiCall('/vendor/coupons?limit=50');
      const coupons = response.data?.coupons || [];
      this.coupons = coupons;
      this.displayCoupons(coupons);
      console.log(`Loaded ${coupons.length} coupons`);

    } catch (error) {
      console.error('Error loading coupons:', error);
      this.showError('Failed to load coupons: ' + error.message);
      this.showCouponsEmpty();
    }
  }

  displayCoupons(coupons) {
    const loadingEl = document.getElementById('coupons-loading');
    const emptyEl = document.getElementById('coupons-empty');
    const containerEl = document.getElementById('coupons-container');

    if (loadingEl) loadingEl.style.display = 'none';

    if (!coupons || coupons.length === 0) {
      if (emptyEl) emptyEl.style.display = 'block';
      if (containerEl) containerEl.style.display = 'none';
      return;
    }

    if (emptyEl) emptyEl.style.display = 'none';
    if (containerEl) {
      containerEl.style.display = 'block';
      containerEl.innerHTML = coupons.map(coupon => this.createCouponHTML(coupon)).join('');
    }

    // Add event listeners for delete buttons
    this.setupCouponActions();
  }

  createCouponHTML(coupon) {
    const expirationDate = new Date(coupon.expiration_date);
    const isExpired = expirationDate <= new Date();
    const formattedDate = expirationDate.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });

    const couponTypeText = coupon.coupon_type === 'discount' ? 'Percentage Discount' : 'Flat Amount Off';
    const discountText = coupon.coupon_type === 'discount'
      ? `${coupon.discount_value}%`
      : `₹${coupon.discount_value}`;

    const targetText = coupon.product_type === 'collection'
      ? `Collection: ${coupon.collection?.name || 'Unknown'}`
      : `Product: ${coupon.product?.name || 'Unknown'}`;

    const statusClass = isExpired ? 'expired' : (coupon.is_active ? 'active' : 'inactive');
    const statusText = isExpired ? 'Expired' : (coupon.is_active ? 'Active' : 'Inactive');

    return `
      <div class="cards__single" data-coupon-id="${coupon.id}">
        <div class="cards__title-wrapper">
          <div class="bold s">Coupon Code: ${coupon.code}</div>
          <div class="coupon-status ${statusClass}">${statusText}</div>
          <button class="btn btn--trash delete-coupon-btn" data-coupon-id="${coupon.id}" data-coupon-code="${coupon.code}">
            <img src="img/icon-trash.svg" alt="" class="btn__icon" />
          </button>
        </div>

        <div class="cards__item">Coupon Type: ${couponTypeText}</div>
        <div class="cards__item">Discount: ${discountText}</div>
        <div class="cards__item">${targetText}</div>
        <div class="cards__item">Expires: ${formattedDate}</div>
        ${coupon.usage_limit ? `<div class="cards__item">Usage Limit: ${coupon.usage_limit}</div>` : ''}
        ${coupon.minimum_order_amount ? `<div class="cards__item">Min Order: ₹${coupon.minimum_order_amount}</div>` : ''}
        <div class="cards__item">Used: ${coupon.used_count || 0} times</div>
      </div>
    `;
  }

  setupCouponActions() {
    const deleteButtons = document.querySelectorAll('.delete-coupon-btn');
    deleteButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const couponId = e.target.closest('.delete-coupon-btn').getAttribute('data-coupon-id');
        const couponCode = e.target.closest('.delete-coupon-btn').getAttribute('data-coupon-code');
        this.deleteCoupon(couponId, couponCode);
      });
    });
  }

  async deleteCoupon(couponId, couponCode) {
    if (!confirm(`Are you sure you want to delete the coupon "${couponCode}"?`)) {
      return;
    }

    try {
      await this.makeApiCall(`/vendor/coupons/${couponId}`, {
        method: 'DELETE'
      });

      this.showSuccess('Coupon deleted successfully!');
      this.loadCoupons(); // Reload coupons list

    } catch (error) {
      console.error('Error deleting coupon:', error);
      this.showError(error.message || 'Failed to delete coupon');
    }
  }

  async loadSidebarCounts() {
    try {
      const token = localStorage.getItem('skeyy_auth_token');
      if (!token) return;

      // Load order statistics
      const orderResponse = await fetch(`${this.apiBaseUrl}/vendor/orders/stats`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (orderResponse.ok) {
        const orderData = await orderResponse.json();
        if (orderData.success) {
          this.updateOrderCounts(orderData.data);
        }
      }

      // Load return statistics
      const returnResponse = await fetch(`${this.apiBaseUrl}/vendor/returns/stats`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (returnResponse.ok) {
        const returnData = await returnResponse.json();
        if (returnData.success) {
          this.updateReturnCounts(returnData.data);
        }
      }

    } catch (error) {
      console.error('Error loading sidebar counts:', error);
    }
  }

  updateOrderCounts(stats) {
    // Update order navigation counts
    const unfulfilledOrderLink = document.querySelector('a[href="orders-unfulfilled.html"]');
    const fulfilledOrderLink = document.querySelector('a[href="orders-fulfilled.html"]');

    if (unfulfilledOrderLink) {
      unfulfilledOrderLink.textContent = `Unfulfilled (${stats.unfulfilled_orders || 0})`;
    }

    if (fulfilledOrderLink) {
      fulfilledOrderLink.textContent = `Fulfilled (${stats.fulfilled_orders || 0})`;
    }
  }

  updateReturnCounts(stats) {
    // Update return navigation counts
    const unfulfilledReturnLink = document.querySelector('a[href="returns-unfulfilled.html"]');
    const fulfilledReturnLink = document.querySelector('a[href="returns-fulfilled.html"]');

    if (unfulfilledReturnLink) {
      unfulfilledReturnLink.textContent = `Unfulfilled (${stats.unfulfilled_returns || 0})`;
    }

    if (fulfilledReturnLink) {
      fulfilledReturnLink.textContent = `Fulfilled (${stats.fulfilled_returns || 0})`;
    }
  }

  showCouponsLoading() {
    const loadingEl = document.getElementById('coupons-loading');
    const emptyEl = document.getElementById('coupons-empty');
    const containerEl = document.getElementById('coupons-container');

    if (loadingEl) loadingEl.style.display = 'block';
    if (emptyEl) emptyEl.style.display = 'none';
    if (containerEl) containerEl.style.display = 'none';
  }

  showCouponsEmpty() {
    const loadingEl = document.getElementById('coupons-loading');
    const emptyEl = document.getElementById('coupons-empty');
    const containerEl = document.getElementById('coupons-container');

    if (loadingEl) loadingEl.style.display = 'none';
    if (emptyEl) emptyEl.style.display = 'block';
    if (containerEl) containerEl.style.display = 'none';
  }

  showLoading() {
    const btn = document.getElementById('js-discount-btn');
    if (btn) {
      btn.disabled = true;
      btn.textContent = 'Creating...';
    }
  }

  hideLoading() {
    const btn = document.getElementById('js-discount-btn');
    if (btn) {
      btn.disabled = false;
      btn.textContent = 'Add Coupon';
    }
  }

  showSuccess(message) {
    alert(message);
  }

  showError(message) {
    alert('Error: ' + message);
  }

  async makeApiCall(endpoint, options = {}) {
    const url = `${this.apiBaseUrl}${endpoint}`;
    const config = {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      ...options
    };

    console.log(`Making API call: ${config.method} ${url}`);

    const response = await fetch(url, config);

    if (!response.ok) {
      if (response.status === 401) {
        alert('Your session has expired. Please login again.');
        localStorage.removeItem('skeyy_auth_token');
        window.location.href = '../frontend/login.html';
        return;
      }

      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        // If we can't parse the error response, use the default message
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || 'API call failed');
    }

    return data;
  }
}

// Initialize the vendor coupon manager when the page loads
let vendorCouponManager;
document.addEventListener('DOMContentLoaded', () => {
  vendorCouponManager = new VendorCouponManager();
});
