<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Skeyy Vendor Panel</title>
    <link rel="stylesheet" href="css/main.min.css" />
  </head>
  <body>
    <div class="grid">
      <div class="grid__nav">
        <nav class="nav">
          <a href="index.html" class="nav__logo-wrapper">
            <img src="img/skey_logo.jpg" alt="sKeyy Logo" class="nav__logo" />
          </a>

          <div class="nav__contents">
            <div class="nav__link-wrapper">
              <a href="index.html" class="nav__link">Dashboard</a>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Orders</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="orders-unfulfilled.html" class="nav__link"
                    >Unfulfilled (0)</a
                  >
                </li>
                <li class="nav__link-list-item">
                  <a href="orders-fulfilled.html" class="nav__link"
                    >Fulfilled (0)</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Returns</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="returns-unfulfilled.html" class="nav__link"
                    >Unfulfilled (0)</a
                  >
                </li>
                <li class="nav__link-list-item">
                  <a href="returns-fulfilled.html" class="nav__link"
                    >Fulfilled (0)</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Products</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="add-product.html" class="nav__link">Add New</a>
                </li>
                <li class="nav__link-list-item">
                  <a href="select-product.html" class="nav__link"
                    >Modify Existing</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Promos & Discounts</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a
                    href="modify-discounts.html"
                    class="nav__link nav__link--active"
                    >Apply discounts</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Support</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="#" class="nav__link">Chat on WhatsApp</a>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </div>

      <div class="grid__main">
        <div class="nav-up">
          <div class="nav-up__left">
            <div class="s bold nav-up__current-page">Dashboard</div>

            <form class="nav-up__search-wrapper" id="js-search-form">
              <img
                src="img/icon-search.svg"
                alt="Search icon"
                class="nav-up__seach-icon"
              />

              <input
                type="text"
                class="nav-up__input-control"
                id="js-search"
                placeholder="Search here..."
              />
            </form>
          </div>

          <div class="nav-up__right">
            <a href="profile.html" class="nav-up__user">
              <div class="nav-up__user-icon-wrapper">
                <img src="img/icon-user.svg" alt="" class="nav-up__user-icon" />
              </div>

              Vendor
            </a>

            <a href="logout.html" class="link">Logout</a>
          </div>
        </div>

        <main class="grid__main">
          <div class="grid__breadcrum">
            <div class="grid__breadcrum-item">
              <a
                href="index.html"
                class="grid__breadcrum-item grid__breadcrum-link"
                >Dashboard</a
              >
            </div>

            <div class="grid__breadcrum-item">Coupon Codes</div>
          </div>

          <div class="discounts">
            <div class="discounts__wrapper">
              <header class="discounts__header">
                <div class="m bold">Create a coupon code</div>
              </header>

              <div class="discounts__form-wrapper">
                <div class="input">
                  <label for="js-type" class="input__label">Product Type</label>

                  <select id="js-type" class="input__control">
                    <option value="">Select Product Type</option>
                    <option value="collection">Collection</option>
                    <option value="product">Specific Product</option>
                  </select>
                </div>

                <div class="input">
                  <label for="js-collection" class="input__label"
                    >Collection / Product</label
                  >

                  <select id="js-collection" class="input__control" disabled>
                    <option value="">Select Product Type First</option>
                  </select>
                </div>

                <div class="input">
                  <label for="js-discount-type" class="input__label"
                    >Coupon Type</label
                  >

                  <select id="js-discount-type" class="input__control">
                    <option value="">Select Coupon Type</option>
                    <option value="discount">Percentage Discount</option>
                    <option value="flat_off">Flat Amount Off</option>
                  </select>
                </div>

                <div class="input">
                  <label for="js-discount" class="input__label"
                    >Discount % / Amount</label
                  >

                  <input
                    type="number"
                    class="input__control"
                    id="js-discount"
                    min="1"
                    step="0.01"
                    placeholder="Enter discount value"
                  />

                  <span class="input__error" style="display: none;">Please enter a valid amount</span>
                </div>

                <div class="input">
                  <label for="js-code" class="input__label">Coupon Code</label>

                  <input
                    type="text"
                    class="input__control"
                    id="js-code"
                    placeholder="e.g., SAVE20, WELCOME10"
                    style="text-transform: uppercase;"
                  />

                  <span class="input__error" style="display: none;">Please enter a coupon code</span>
                </div>

                <div class="input">
                  <label for="js-date" class="input__label"
                    >Expiration Date</label
                  >

                  <input
                    type="date"
                    class="input__control"
                    id="js-date"
                  />

                  <span class="input__error" style="display: none;">Please select an expiration date</span>
                </div>

                <div class="input">
                  <label for="js-usage-limit" class="input__label"
                    >Usage Limit (Optional)</label
                  >

                  <input
                    type="number"
                    class="input__control"
                    id="js-usage-limit"
                    min="1"
                    placeholder="Leave empty for unlimited"
                  />
                </div>

                <div class="input">
                  <label for="js-min-order" class="input__label"
                    >Minimum Order Amount (Optional)</label
                  >

                  <input
                    type="number"
                    class="input__control"
                    id="js-min-order"
                    min="0"
                    step="0.01"
                    placeholder="Leave empty for no minimum"
                  />
                </div>

                <div class="discounts__btn-wrapper">
                  <button class="btn btn--primary" id="js-discount-btn">
                    Add Coupon
                  </button>
                </div>
              </div>
            </div>

            <div class="discounts__wrapper">
              <header class="discounts__header">
                <div class="m bold">Existing Coupons</div>
              </header>

              <!-- Loading State -->
              <div id="coupons-loading" class="loading-container">
                <div class="loading-message">Loading coupons...</div>
              </div>

              <!-- Empty State -->
              <div id="coupons-empty" class="empty-container" style="display: none;">
                <div class="empty-message">
                  <h3>No Coupons Found</h3>
                  <p>Create your first coupon using the form above.</p>
                </div>
              </div>

              <!-- Coupons Container -->
              <div id="coupons-container" class="cards" style="display: none;">
                <!-- Coupons will be dynamically loaded here -->
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <footer class="footer">Copyright 2025 - sKeyy</footer>
    <!-- JavaScript -->
    <script src="js/vendor-auth.js"></script>
    <script src="js/vendor-utils.js"></script>
    <script src="js/vendor-coupons.js"></script>
  </body>
</html>
