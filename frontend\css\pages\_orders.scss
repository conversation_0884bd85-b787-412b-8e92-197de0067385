.orders {
  &__single {
    display: grid;
    grid-template-columns: max-content 1fr;

    @include respond(600px) {
      grid-template-columns: 1fr;
      border: 3px solid $color-black;
    }

    &:not(:last-child) {
      margin-bottom: 3.2rem;
    }
  }

  &__preview-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    @include respond(600px) {
      gap: 0;
    }
  }

  &__number {
    padding: 2rem;
    background-color: $color-black;

    color: $color-white;
  }

  &__track {
    padding: 1rem;
    background-color: $color-primary;

    color: $color-white;

    &:hover {
      background-color: $color-secondary;
    }
  }

  &__details-wrapper {
    padding: 2.4rem 3.2rem;
    border: 3px solid $color-black;

    @include respond(600px) {
      border: unset;
    }
  }

  &__title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;

    margin-bottom: 2.4rem;

    @include respond(500px) {
      align-items: flex-start;
      flex-direction: column;
    }
  }

  &__item {
    display: grid;
    grid-template-columns: 11.2rem 1fr;

    &:not(:last-child) {
      margin-bottom: 1.2rem;
    }

    @include respond(500px) {
      grid-template-columns: 1fr;
    }
  }

  &__img {
    width: 100%;
    height: 100%;
  }

  &__item-details {
    padding: 2rem;
    background-color: $color-accent-bg;
  }

  &__item-name {
    margin-bottom: 1.2rem;
  }

  &__item-info {
  }

  &__item-totals {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include respond(500px) {
      align-items: flex-start;
      flex-direction: column;
    }
  }
}

// Pagination styles
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 2rem 0;

  &__btn {
    padding: 0.8rem 1.2rem;
    border: 2px solid $color-black;
    background-color: $color-white;
    color: $color-black;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: $color-black;
      color: $color-white;
    }

    &--active {
      background-color: $color-primary;
      border-color: $color-primary;
      color: $color-white;

      &:hover {
        background-color: $color-secondary;
        border-color: $color-secondary;
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: $color-white;
        color: $color-black;
      }
    }
  }
}
