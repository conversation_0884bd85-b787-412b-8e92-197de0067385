<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Skeyy Vendor Panel</title>
    <link rel="stylesheet" href="css/main.min.css" />
  </head>
  <body>
    <div class="grid">
      <div class="grid__nav">
        <nav class="nav">
          <a href="index.html" class="nav__logo-wrapper">
            <img src="img/skey_logo.jpg" alt="sKeyy Logo" class="nav__logo" />
          </a>

          <div class="nav__contents">
            <div class="nav__link-wrapper">
              <a href="index.html" class="nav__link">Dashboard</a>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Orders</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="orders-unfulfilled.html" class="nav__link"
                    >Unfulfilled (0)</a
                  >
                </li>
                <li class="nav__link-list-item">
                  <a href="orders-fulfilled.html" class="nav__link"
                    >Fulfilled (0)</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Returns</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="returns-unfulfilled.html" class="nav__link"
                    >Unfulfilled (0)</a
                  >
                </li>
                <li class="nav__link-list-item">
                  <a href="returns-fulfilled.html" class="nav__link"
                    >Fulfilled (0)</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Products</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="add-product.html" class="nav__link">Add New</a>
                </li>
                <li class="nav__link-list-item">
                  <a href="select-product.html" class="nav__link"
                    >Modify Existing</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Promos & Discounts</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="modify-discounts.html" class="nav__link"
                    >Apply discounts</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Support</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="#" class="nav__link">Chat on WhatsApp</a>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </div>

      <div class="grid__main">
        <div class="nav-up">
          <div class="nav-up__left">
            <div class="s bold nav-up__current-page">Dashboard</div>

            <form class="nav-up__search-wrapper" id="js-search-form">
              <img
                src="img/icon-search.svg"
                alt="Search icon"
                class="nav-up__seach-icon"
              />

              <input
                type="text"
                class="nav-up__input-control"
                id="js-search"
                placeholder="Search here..."
              />
            </form>
          </div>

          <div class="nav-up__right">
            <a href="profile.html" class="nav-up__user">
              <div class="nav-up__user-icon-wrapper">
                <img src="img/icon-user.svg" alt="" class="nav-up__user-icon" />
              </div>

              Vendor
            </a>

            <a href="logout.html" class="link">Logout</a>
          </div>
        </div>

        <main class="grid__main">
          <div class="grid__breadcrum">
            <div class="grid__breadcrum-item">
              <a
                href="index.html"
                class="grid__breadcrum-item grid__breadcrum-link"
                >Dashboard</a
              >
            </div>
            <div class="grid__breadcrum-item">My Profile</div>
          </div>

          <section class="users">
            <div class="users__single">
              <div class="users__title-wrapper">
                <div class="bold l" id="vendor-name-title">Loading...</div>

                <p>Account Status: <span class="brand" id="account-status">Loading...</span></p>
              </div>
            </div>

            <div class="users__grid">
              <div class="users__grid-item">
                <div class="s bold">Contact Info
                  <button class="btn btn--ghost" id="edit-contact-btn" style="margin-left: 1rem; padding: 0.5rem 1rem; font-size: 1.4rem;">Edit</button>
                </div>
                <div id="business-name">Business Name: Loading...</div>
                <div id="email-address">Email Address: Loading...</div>
                <div id="phone-number">Phone Number: Loading...</div>
              </div>

              <div class="users__grid-item">
                <div class="s bold">Bank Info
                  <button class="btn btn--ghost" id="edit-bank-btn" style="margin-left: 1rem; padding: 0.5rem 1rem; font-size: 1.4rem;">Edit</button>
                </div>
                <div id="bank-name">Bank Name and Branch: Loading...</div>
                <div id="pan-number">PAN Number: Loading...</div>
              </div>

              <div class="users__grid-item">
                <div class="s bold">GST Info
                  <button class="btn btn--ghost" id="edit-gst-btn" style="margin-left: 1rem; padding: 0.5rem 1rem; font-size: 1.4rem;">Edit</button>
                </div>
                <div id="gst-business-name">Business Name: Loading...</div>
                <div id="gst-number">GST Number: Loading...</div>
              </div>

              <div class="users__grid-item">
                <div class="s bold">Uploaded Documents</div>
                <div id="business-license">Business License: <span class="link">Loading...</span></div>
                <div id="gst-certificate">GST Certificate: <span class="link">Loading...</span></div>
                <div id="cancelled-cheque">Cancelled Cheque: <span class="link">Loading...</span></div>
                <div style="margin-top: 1rem;">
                  <button class="btn btn--ghost" id="upload-documents-btn" style="padding: 0.5rem 1rem; font-size: 1.4rem;">Upload Documents</button>
                </div>
              </div>
            </div>
          </section>

          <section class="users__update">
            <h2 class="s">Update Password</h2>
            <div id="password-message" class="message" style="display: none; padding: 1rem; margin-bottom: 1rem; border-radius: 4px;"></div>

            <div class="users__input-wrapper">
              <div class="input">
                <label for="js-current-password" class="input__label">Current Password*</label>
                <div style="position: relative;">
                  <input
                    type="password"
                    class="input__control"
                    id="js-current-password"
                    placeholder="Enter your current password"
                  />
                  <button type="button" class="password-toggle" id="toggle-current-password" style="position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; font-size: 1.4rem;">👁️</button>
                </div>
                <span class="input__error">Please enter your current password</span>
              </div>

              <div class="input">
                <label for="js-password" class="input__label">New Password*</label>
                <div style="position: relative;">
                  <input
                    type="password"
                    class="input__control"
                    id="js-password"
                    placeholder="Enter new password (min 6 characters)"
                  />
                  <button type="button" class="password-toggle" id="toggle-new-password" style="position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; font-size: 1.4rem;">👁️</button>
                </div>
                <span class="input__error">Please enter a valid Password</span>
              </div>

              <div class="input">
                <label for="js-confirm-password" class="input__label"
                  >Confirm New Password*</label
                >
                <div style="position: relative;">
                  <input
                    type="password"
                    class="input__control"
                    id="js-confirm-password"
                    placeholder="Confirm your new password"
                  />
                  <button type="button" class="password-toggle" id="toggle-confirm-password" style="position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer; font-size: 1.4rem;">👁️</button>
                </div>
                <span class="input__error">The passwords do not match.</span>
              </div>
            </div>
            <div class="users__btn-wrapper">
              <button class="btn btn--primary" id="js-update-password">
                Update Password
              </button>
            </div>
          </section>
        </main>
      </div>
    </div>

    <!-- Edit Contact Info Modal -->
    <div class="modal" id="contact-modal">
      <div class="modal__bg"></div>
      <div class="modal__container">
        <div class="modal__close-wrapper">
          <button class="modal__btn" id="close-contact-modal">×</button>
        </div>
        <div class="modal__header">
          <h2 class="s">Edit Contact Information</h2>
        </div>
        <form id="contact-form">
          <div class="modal__input-wrapper">
            <div class="input">
              <label for="edit-business-name" class="input__label">Business Name*</label>
              <input type="text" class="input__control" id="edit-business-name" required />
              <span class="input__error">Please enter a valid business name</span>
            </div>
            <div class="input">
              <label for="edit-email" class="input__label">Email Address*</label>
              <input type="email" class="input__control" id="edit-email" required />
              <span class="input__error">Please enter a valid email address</span>
            </div>
          </div>
          <div class="input">
            <label for="edit-phone" class="input__label">Phone Number*</label>
            <input type="tel" class="input__control" id="edit-phone" required />
            <span class="input__error">Please enter a valid phone number</span>
          </div>
          <div class="modal__btn-wrapper">
            <button type="submit" class="btn btn--primary">Update Contact Info</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Edit Bank Info Modal -->
    <div class="modal" id="bank-modal">
      <div class="modal__bg"></div>
      <div class="modal__container">
        <div class="modal__close-wrapper">
          <button class="modal__btn" id="close-bank-modal">×</button>
        </div>
        <div class="modal__header">
          <h2 class="s">Edit Bank Information</h2>
        </div>
        <form id="bank-form">
          <div class="modal__input-wrapper">
            <div class="input">
              <label for="edit-bank-name" class="input__label">Bank Name and Branch</label>
              <input type="text" class="input__control" id="edit-bank-name" />
              <span class="input__error">Please enter a valid bank name</span>
            </div>
            <div class="input">
              <label for="edit-pan" class="input__label">PAN Number</label>
              <input type="text" class="input__control" id="edit-pan" maxlength="10" />
              <span class="input__error">Please enter a valid PAN number</span>
            </div>
          </div>
          <div class="modal__btn-wrapper">
            <button type="submit" class="btn btn--primary">Update Bank Info</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Edit GST Info Modal -->
    <div class="modal" id="gst-modal">
      <div class="modal__bg"></div>
      <div class="modal__container">
        <div class="modal__close-wrapper">
          <button class="modal__btn" id="close-gst-modal">×</button>
        </div>
        <div class="modal__header">
          <h2 class="s">Edit GST Information</h2>
        </div>
        <form id="gst-form">
          <div class="input">
            <label for="edit-gst-number" class="input__label">GST Number</label>
            <input type="text" class="input__control" id="edit-gst-number" maxlength="15" />
            <span class="input__error">Please enter a valid GST number</span>
          </div>
          <div class="input">
            <label for="edit-business-address" class="input__label">Business Address</label>
            <textarea class="input__control input__control--textarea" id="edit-business-address" rows="4"></textarea>
            <span class="input__error">Please enter a valid business address</span>
          </div>
          <div class="modal__btn-wrapper">
            <button type="submit" class="btn btn--primary">Update GST Info</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Upload Documents Modal -->
    <div class="modal" id="documents-modal">
      <div class="modal__bg"></div>
      <div class="modal__container">
        <div class="modal__close-wrapper">
          <button class="modal__btn" id="close-documents-modal">×</button>
        </div>
        <div class="modal__header">
          <h2 class="s">Upload Documents</h2>
          <p>Upload your business documents for verification. Accepted formats: PDF, JPEG, PNG, GIF (Max 10MB each)</p>
        </div>
        <div class="modal__details">
          <div class="input">
            <label for="upload-business-license" class="input__label">Business License</label>
            <input type="file" class="input__control" id="upload-business-license" accept=".pdf,.jpg,.jpeg,.png,.gif" />
            <span class="input__error">Please select a valid file</span>
            <div class="upload-progress" id="business-license-progress" style="display: none;">
              <div class="upload-progress-bar"></div>
              <span class="upload-progress-text">Uploading...</span>
            </div>
          </div>
          <div class="input">
            <label for="upload-gst-certificate" class="input__label">GST Certificate</label>
            <input type="file" class="input__control" id="upload-gst-certificate" accept=".pdf,.jpg,.jpeg,.png,.gif" />
            <span class="input__error">Please select a valid file</span>
            <div class="upload-progress" id="gst-certificate-progress" style="display: none;">
              <div class="upload-progress-bar"></div>
              <span class="upload-progress-text">Uploading...</span>
            </div>
          </div>
          <div class="input">
            <label for="upload-cancelled-cheque" class="input__label">Cancelled Cheque</label>
            <input type="file" class="input__control" id="upload-cancelled-cheque" accept=".pdf,.jpg,.jpeg,.png,.gif" />
            <span class="input__error">Please select a valid file</span>
            <div class="upload-progress" id="cancelled-cheque-progress" style="display: none;">
              <div class="upload-progress-bar"></div>
              <span class="upload-progress-text">Uploading...</span>
            </div>
          </div>
        </div>
        <div class="modal__btn-wrapper">
          <button type="button" class="btn btn--primary" id="upload-all-documents">Upload Selected Documents</button>
        </div>
      </div>
    </div>

    <footer class="footer">Copyright 2025 - sKeyy</footer>

    <!-- JavaScript -->
    <script src="js/vendor-auth.js"></script>
    <script src="js/vendor-utils.js"></script>
    <script src="js/vendor-dashboard.js"></script>
    <script src="js/vendor-profile.js"></script>
  </body>
</html>
