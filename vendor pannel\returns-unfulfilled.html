<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Skeyy Vendor Panel</title>
    <link rel="stylesheet" href="css/main.min.css" />
  </head>
  <body>
    <div class="grid">
      <div class="grid__nav">
        <nav class="nav">
          <a href="index.html" class="nav__logo-wrapper">
            <img src="img/skey_logo.jpg" alt="sKeyy Logo" class="nav__logo" />
          </a>

          <div class="nav__contents">
            <div class="nav__link-wrapper">
              <a href="index.html" class="nav__link">Dashboard</a>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Orders</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="orders-unfulfilled.html" class="nav__link"
                    >Unfulfilled (0)</a
                  >
                </li>
                <li class="nav__link-list-item">
                  <a href="orders-fulfilled.html" class="nav__link"
                    >Fulfilled (0)</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Returns</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a
                    href="returns-unfulfilled.html"
                    class="nav__link nav__link--active"
                    >Unfulfilled (0)</a
                  >
                </li>
                <li class="nav__link-list-item">
                  <a href="returns-fulfilled.html" class="nav__link"
                    >Fulfilled (0)</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Products</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="add-product.html" class="nav__link">Add New</a>
                </li>
                <li class="nav__link-list-item">
                  <a href="select-product.html" class="nav__link"
                    >Modify Existing</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Promos & Discounts</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="modify-discounts.html" class="nav__link"
                    >Apply discounts</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Support</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="#" class="nav__link">Chat on WhatsApp</a>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </div>

      <div class="grid__main">
        <div class="nav-up">
          <div class="nav-up__left">
            <div class="s bold nav-up__current-page">Unfulfilled Returns</div>

            <form class="nav-up__search-wrapper" id="js-search-form">
              <img
                src="img/icon-search.svg"
                alt="Search icon"
                class="nav-up__seach-icon"
              />

              <input
                type="text"
                class="nav-up__input-control"
                id="js-search"
                placeholder="Search here..."
              />
            </form>
          </div>

          <div class="nav-up__right">
            <a href="profile.html" class="nav-up__user">
              <div class="nav-up__user-icon-wrapper">
                <img src="img/icon-user.svg" alt="" class="nav-up__user-icon" />
              </div>

              Vendor
            </a>

            <a href="logout.html" class="link">Logout</a>
          </div>
        </div>

        <main class="grid__main">
          <div class="grid__breadcrum">
            <div class="grid__breadcrum-item">
              <a
                href="index.html"
                class="grid__breadcrum-item grid__breadcrum-link"
                >Dashboard</a
              >
            </div>

            <div class="grid__breadcrum-item">Unfulfilled Returns</div>
          </div>

          <!-- Loading State -->
          <div id="loading-state" class="loading-container">
            <div class="loading-message">Loading unfulfilled returns...</div>
          </div>

          <!-- Empty State -->
          <div id="empty-state" class="empty-container" style="display: none;">
            <div class="empty-message">
              <h3>No Unfulfilled Returns</h3>
              <p>All returns have been processed!</p>
            </div>
          </div>

          <!-- Returns Container -->
          <div id="returns-container" class="product" style="display: none;">
            <!-- Returns will be dynamically loaded here -->
          </div>

          <!-- Pagination -->
          <div id="pagination-container" class="pagination-container" style="display: none;">
            <div class="pagination">
              <button id="prev-page" class="pagination-btn" disabled>Previous</button>
              <span id="page-info">Page 1 of 1</span>
              <button id="next-page" class="pagination-btn" disabled>Next</button>
            </div>
          </div>
        </main>
      </div>
    </div>

    <footer class="footer">Copyright 2025 - sKeyy</footer>
    <!-- JavaScript -->
    <script src="js/vendor-auth.js"></script>
    <script src="js/vendor-utils.js"></script>
    <script src="js/vendor-returns-unfulfilled.js"></script>
  </body>
</html>
