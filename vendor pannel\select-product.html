<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>Skeyy Vendor Panel</title>
    <link rel="stylesheet" href="css/main.min.css?v=4" />
    <style>
      /* Force hide any cached/static content */
      .product__cards-wrapper .product-card:not([data-dynamic="true"]) {
        display: none !important;
      }

      /* Ensure our dynamic content is visible */
      .product__cards-wrapper [data-dynamic="true"],
      .product__cards-wrapper .loading-message,
      .product__cards-wrapper .initial-message,
      .product__cards-wrapper .no-products,
      .product__cards-wrapper .error-message {
        display: block !important;
      }

      /* Override any background colors that might create placeholder boxes */
      .product__cards-wrapper::before,
      .product__cards-wrapper::after {
        display: none !important;
      }

      /* Product Card Action Styles */
      .product-card__actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
      }

      .btn--danger {
        background: #dc3545 !important;
        color: white !important;
        border: 1px solid #dc3545 !important;
        transition: all 0.3s ease;
      }

      .btn--danger:hover {
        background: #c82333 !important;
        border-color: #bd2130 !important;
        transform: translateY(-1px);
      }

      .btn--danger:active {
        transform: translateY(0);
      }

      .product-card__actions .btn {
        flex: 1;
        text-align: center;
        font-size: 1.2rem;
        padding: 0.8rem 1rem;
      }

      /* Delete confirmation modal styles */
      .delete-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      }

      .delete-modal__content {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        max-width: 400px;
        width: 90%;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      }

      .delete-modal__title {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 1rem;
        color: #dc3545;
      }

      .delete-modal__message {
        font-size: 1.4rem;
        margin-bottom: 2rem;
        color: #666;
      }

      .delete-modal__actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
      }

      .delete-modal__actions .btn {
        padding: 1rem 2rem;
        font-size: 1.4rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .delete-modal__cancel {
        background: #6c757d;
        color: white;
      }

      .delete-modal__cancel:hover {
        background: #5a6268;
      }

      .delete-modal__confirm {
        background: #dc3545;
        color: white;
      }

      .delete-modal__confirm:hover {
        background: #c82333;
      }

      /* Enhanced Product Card Styles */
      .product-card {
        position: relative;
        overflow: hidden;
      }

      .product-card__discount {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #dc3545;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 1.2rem;
        font-weight: bold;
        z-index: 2;
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
      }

      .product-card__category {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 0.5rem;
        font-weight: 500;
        text-transform: capitalize;
        background: #f8f9fa;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        display: inline-block;
        border: 1px solid #e9ecef;
      }

      .product-card__price {
        display: flex;
        align-items: center;
        gap: 0.8rem;
        margin: 0.8rem 0;
      }

      .product-card__original-price {
        font-size: 1.2rem;
        color: #999;
        text-decoration: line-through;
        font-weight: normal;
      }

      .product-card__final-price {
        font-size: 1.4rem;
        color: #28a745;
        font-weight: bold;
      }

      .product-card__stock {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 1rem;
        padding: 0.3rem 0.8rem;
        background: #e8f5e8;
        border-radius: 10px;
        display: inline-block;
        border: 1px solid #c3e6cb;
      }

      /* Category breadcrumb styling */
      .product-card__category::before {
        content: "📂 ";
        margin-right: 0.3rem;
      }
    </style>
  </head>
  <body>
    <div class="grid">
      <div class="grid__nav">
        <nav class="nav">
          <a href="index.html" class="nav__logo-wrapper">
            <img src="img/skey_logo.jpg" alt="sKeyy Logo" class="nav__logo" />
          </a>

          <div class="nav__contents">
            <div class="nav__link-wrapper">
              <a href="index.html" class="nav__link">Dashboard</a>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Orders</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="orders-unfulfilled.html" class="nav__link"
                    >Unfulfilled (0)</a
                  >
                </li>
                <li class="nav__link-list-item">
                  <a href="orders-fulfilled.html" class="nav__link"
                    >Fulfilled (0)</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Returns</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="returns-unfulfilled.html" class="nav__link"
                    >Unfulfilled (0)</a
                  >
                </li>
                <li class="nav__link-list-item">
                  <a href="returns-fulfilled.html" class="nav__link"
                    >Fulfilled (0)</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Products</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="add-product.html" class="nav__link">Add New</a>
                </li>
                <li class="nav__link-list-item">
                  <a
                    href="select-product.html"
                    class="nav__link nav__link--active"
                    >Modify Existing</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Promos & Discounts</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="modify-discounts.html" class="nav__link"
                    >Apply discounts</a
                  >
                </li>
              </ul>
            </div>

            <div class="nav__link-wrapper">
              <div class="nav__link-title bold">Support</div>
              <ul class="nav__link-list">
                <li class="nav__link-list-item">
                  <a href="#" class="nav__link">Chat on WhatsApp</a>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </div>

      <div class="grid__main">
        <div class="nav-up">
          <div class="nav-up__left">
            <div class="s bold nav-up__current-page">Dashboard</div>

            <form class="nav-up__search-wrapper" id="js-search-form">
              <img
                src="img/icon-search.svg"
                alt="Search icon"
                class="nav-up__seach-icon"
              />

              <input
                type="text"
                class="nav-up__input-control"
                id="js-search"
                placeholder="Search here..."
              />
            </form>
          </div>

          <div class="nav-up__right">
            <a href="profile.html" class="nav-up__user">
              <div class="nav-up__user-icon-wrapper">
                <img src="img/icon-user.svg" alt="" class="nav-up__user-icon" />
              </div>

              Vendor
            </a>

            <a href="logout.html" class="link">Logout</a>
          </div>
        </div>

        <main class="grid__main">
          <div class="grid__breadcrum">
            <div class="grid__breadcrum-item">
              <a
                href="index.html"
                class="grid__breadcrum-item grid__breadcrum-link"
                >Dashboard</a
              >
            </div>
            <div class="grid__breadcrum-item">Select Product</div>
          </div>

          <section class="product">
            <section class="product__section">
              <h2 class="m bold product__section-title">Collection Details</h2>
              <div class="input">
                <label for="js-collection" class="input__label"
                  >Product Collection*</label
                >

                <select id="js-collection" class="input__control">
                  <option value="">All Categories</option>
                  <!-- Categories will be loaded dynamically -->
                </select>
              </div>
            </section>

            <div class="product__cards-wrapper" id="products-container">
              <!-- Products will be loaded here dynamically -->
              <div class="initial-message" style="grid-column: 1 / -1; text-align: center; padding: 3rem; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px;">
                <div style="font-size: 1.6rem; margin-bottom: 1rem;">⏳ Initializing...</div>
                <div style="color: #666; font-size: 1.4rem; margin-bottom: 2rem;">If this message persists, please check the browser console (F12) for errors.</div>
                <button onclick="window.forceReload()" style="padding: 1rem 2rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                  🔄 Force Reload Products
                </button>
              </div>
            </div>
          </section>
        </main>
      </div>
    </div>

    <footer class="footer">Copyright 2025 - sKeyy</footer>

    <!-- Debug utilities -->
    <script>
      // Force reload function for debugging
      window.forceReload = function() {
        console.log('🔄 Force reload triggered');
        location.reload(true);
      };
    </script>

    <!-- Main JavaScript -->
    <script src="js/vendor-auth.js?v=16"></script>
    <script src="js/vendor-utils.js"></script>
    <script src="js/vendor-product-listing.js?v=16"></script>
  </body>
</html>
