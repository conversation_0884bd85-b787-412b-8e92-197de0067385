@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap");
body {
  font-family: "Roboto", sans-serif;
  font-size: 2rem;
  line-height: 1.5;
}

.xxxl {
  font-size: clamp(8rem, 10vw + 1rem, 12rem);
  line-height: 1.3;
}

.xxl {
  font-size: clamp(6rem, 8vw + 1rem, 8rem);
  line-height: 1.3;
}

.xl {
  font-size: clamp(4.8rem, 6vw + 1rem, 6rem);
  line-height: 1.3;
}

.l {
  font-size: clamp(3.6rem, 4.8vw + 1rem, 4.8rem);
  line-height: 1.3;
}

.m {
  font-size: clamp(2.8rem, 3.6vw + 1rem, 3.6rem);
  line-height: 1.5;
}

.s {
  font-size: 2.4rem;
  line-height: 1.7;
}

.copy {
  font-size: 2rem;
  line-height: 1.5;
}

*,
*::after,
*::before {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
}

html {
  font-size: 62.5%;
}
@media screen and (max-width: 64em) {
  html {
    font-size: 56.25%;
  }
}
@media screen and (max-width: 48em) {
  html {
    font-size: 50%;
  }
}

body {
  box-sizing: border-box;
  background-color: #fff;
  min-height: 100vh;
  background-color: #f6f6f6;
  overflow-x: hidden;
}

::selection {
  background-color: #575fcf;
  color: #fff;
}

a {
  text-decoration: none;
}

a,
button {
  display: inline-block;
}

button,
input,
textarea {
  outline: none;
  border: none;
  font-family: inherit;
  font-size: inherit;
}

ol,
ul {
  list-style: none;
}

img {
  display: block;
  max-width: 100%;
  border: 0;
  object-fit: cover;
}

.bold {
  font-weight: 700;
}

.normal {
  font-weight: 400;
}

.light {
  font-weight: 300;
}

.container {
  width: 100%;
  max-width: 127.8rem;
  margin: 0 auto;
}

.btn {
  display: block;
  position: relative;
  z-index: 1;
  padding: 1.6rem 3rem;
  text-align: center;
  font-weight: 700;
  overflow: hidden;
  cursor: pointer;
}
.btn--primary {
  background-color: #575fcf;
  color: #fff;
}
.btn--primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-color: #3c40c6;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.2s ease-in-out;
}
.btn--primary:hover::before {
  transform: scaleX(1);
}
.btn--full {
  width: 100%;
}
.btn--ghost {
  height: 100%;
  padding: 2rem;
  background-color: transparent;
  border: 2px solid #575fcf;
  color: #575fcf;
  transition: background-color 0.3s;
}
.btn--ghost:hover {
  background-color: #575fcf;
  color: #fff;
}
.btn--trash {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 5.5rem;
  height: 5.5rem;
  padding: 1rem;
  background-color: transparent;
  border: 2px solid red;
  transition: background-color 0.3s, border-color 0.3s;
}
.btn--trash:hover {
  border-color: #cc0000;
  background-color: #cc0000;
}
.btn--trash .btn__icon {
  width: 2.4rem;
  height: 2.4rem;
}
.btn .btn:disabled {
  opacity: 0.3;
  pointer-events: none;
}

.input:not(:last-child) {
  margin-bottom: 2.4rem;
}
.input--radio:not(:last-child) {
  margin-bottom: 0.4rem;
}
.input--radio .input__label {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.8rem;
}
.input--radio .input__control {
  width: max-content;
}
.input__label {
  display: block;
}
.input__control {
  display: block;
  width: 100%;
  padding: 2rem;
  background-color: #f6f6fe;
  border: 1px solid #ababab;
  font-size: inherit;
}
.input__control--textarea {
  resize: vertical;
  min-height: 20rem;
}
.input__control--error + .input__error {
  display: block;
}
.input__error {
  display: none;
  color: red;
}

.link {
  background-color: transparent;
  color: #575fcf;
  cursor: pointer;
}
.link:hover {
  color: #3c40c6;
}

.toast {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 4;
  max-width: 800px;
  padding: 10px 20px;
  margin: auto;
  background: #cccccc;
  font-family: inherit;
  text-align: center;
  color: #333333;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s, top 0.2s, visibility 0.2s;
  border-radius: 3px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}
.toast--visible {
  bottom: 20px;
  opacity: 1;
  visibility: visible;
}
.toast--success {
  background: #00c02b;
  border-color: #009d23;
  color: #ffffff;
}
.toast--error {
  background: #d50000;
  border-color: #ba0000;
  color: #ffffff;
}

.nav {
  padding: 1.8rem 3.2rem 8rem;
  min-width: 32rem;
  background-color: #fff;
  height: 100%;
}
@media screen and (max-width: 50em) {
  .nav {
    padding: 2.4rem;
  }
}
@media screen and (max-width: 20em) {
  .nav {
    padding: 1rem;
  }
}
.nav__logo-wrapper {
  margin-bottom: 3.2rem;
}
.nav__logo {
  max-width: 10rem;
}
@media screen and (max-width: 50em) {
  .nav__contents {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 2rem;
  }
}
@media screen and (max-width: 33.75em) {
  .nav__contents {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 20em) {
  .nav__contents {
    grid-template-columns: 1fr;
    grid-gap: 1rem;
  }
}
.nav__link-wrapper:not(:last-child) {
  margin-bottom: 3.2rem;
}
@media screen and (max-width: 50em) {
  .nav__link-wrapper {
    padding: 2rem;
    background-color: #f6f6f6;
  }
  .nav__link-wrapper:not(:last-child) {
    margin-bottom: unset;
  }
}
.nav__link {
  color: #000;
}
.nav__link--active {
  color: #575fcf;
  font-weight: 700;
}
.nav__link:hover {
  color: #575fcf;
}
.nav__link-list {
  margin-bottom: 1.2rem;
}
.nav__link-list-item:not(:last-child) {
  margin-bottom: 0.4rem;
}

.nav-up {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  padding: 2rem 2.4rem;
  background-color: #fff;
}
@media screen and (max-width: 37.5em) {
  .nav-up {
    flex-direction: column;
    align-items: flex-start;
  }
}
.nav-up__left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 3.2rem;
}
@media screen and (max-width: 37.5em) {
  .nav-up__left {
    align-items: stretch;
    width: 100%;
  }
}
.nav-up__search-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1.2rem;
  background-color: #f6f6f6;
  padding: 0.5rem 2rem;
}
@media screen and (max-width: 37.5em) {
  .nav-up__search-wrapper {
    width: 100%;
  }
}
.nav-up__input-control {
  padding: 1rem 0;
  background-color: transparent;
  min-width: 32rem;
}
@media screen and (max-width: 73.125em) {
  .nav-up__input-control {
    min-width: unset;
    width: 100%;
  }
}
.nav-up__right {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 2rem;
}
@media screen and (max-width: 37.5em) {
  .nav-up__right {
    justify-content: flex-end;
    width: 100%;
  }
}
.nav-up__user {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.8rem;
  color: #000;
}
.nav-up__user:hover {
  color: #575fcf;
}
.nav-up__user:hover .nav-up__user-icon-wrapper {
  background-color: #575fcf;
}
.nav-up__user-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 5rem;
  height: 5rem;
  padding: 1rem;
  background-color: #f6f6fe;
  border-radius: 50%;
  transition: background-color 0.3s ease-in-out;
}
.footer {
  margin-top: 1.6rem;
  padding: 2rem;
  background-color: #fff;
  text-align: center;
}

/* Vendor/Admin Product Distinction Styles */
.product-card__vendor {
  font-size: 0.85rem;
  margin: 0.5rem 0;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  border-left: 3px solid;
}

.vendor-product {
  background-color: #e3f2fd;
  border-left-color: #2196f3;
  color: #1565c0;
}

.admin-product {
  background-color: #f3e5f5;
  border-left-color: #9c27b0;
  color: #7b1fa2;
}

.orders__item-vendor {
  font-size: 0.85rem;
  margin: 0.3rem 0;
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  border-left: 2px solid;
}

.vendor-item {
  background-color: #e8f5e8;
  border-left-color: #4caf50;
  color: #2e7d32;
}

.admin-item {
  background-color: #fff3e0;
  border-left-color: #ff9800;
  color: #f57c00;
}

.grid {
  display: grid;
  grid-template-columns: max-content 1fr;
  grid-gap: 1.6rem;
  padding-right: 2rem;
}
@media screen and (max-width: 50em) {
  .grid {
    grid-template-columns: 1fr;
    padding-right: unset;
  }
}
@media screen and (max-width: 50em) {
  .grid__main {
    padding: 1rem;
  }
}
.grid__breadcrum {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
  padding: 2rem 0;
}
.grid__breadcrum-item:not(:last-child)::after {
  content: ">";
}
.grid__breadcrum-link {
  color: #575fcf;
  text-decoration: underline;
}
.grid__breadcrum-link:hover {
  color: #3c40c6;
}

.orders__single {
  display: grid;
  grid-template-columns: max-content 1fr;
}
@media screen and (max-width: 37.5em) {
  .orders__single {
    grid-template-columns: 1fr;
    border: 3px solid #000;
  }
}
.orders__single:not(:last-child) {
  margin-bottom: 3.2rem;
}
.orders__preview-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
@media screen and (max-width: 37.5em) {
  .orders__preview-wrapper {
    gap: 0;
  }
}
.orders__number {
  padding: 2rem;
  background-color: #000;
  color: #fff;
}
.orders__track {
  padding: 1rem;
  background-color: #575fcf;
  color: #fff;
}
.orders__track:hover {
  background-color: #3c40c6;
}
.orders__details-wrapper {
  padding: 2.4rem 3.2rem;
  border: 3px solid #000;
  background-color: #fff;
}
@media screen and (max-width: 37.5em) {
  .orders__details-wrapper {
    border: unset;
  }
}
.orders__title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2.4rem;
}
@media screen and (max-width: 31.25em) {
  .orders__title-wrapper {
    align-items: flex-start;
    flex-direction: column;
  }
}
.orders__item {
  display: grid;
  grid-template-columns: 11.2rem 1fr;
}
.orders__item:not(:last-child) {
  margin-bottom: 1.2rem;
}
@media screen and (max-width: 31.25em) {
  .orders__item {
    grid-template-columns: 1fr;
  }
}
.orders__img {
  width: 100%;
  height: 100%;
}
.orders__item-details {
  padding: 2rem;
  background-color: #f6f6fe;
}
.orders__item-name {
  margin-bottom: 1.2rem;
}
.orders__item-totals {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media screen and (max-width: 31.25em) {
  .orders__item-totals {
    align-items: flex-start;
    flex-direction: column;
  }
}

.cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2.4rem;
}
@media screen and (max-width: 65em) {
  .cards {
    grid-template-columns: 1fr;
  }
}
.cards__single {
  padding: 2rem;
  border-left: 1.2rem solid #575fcf;
  border: 2px solid #575fcf;
  border-left: 2rem solid #575fcf;
}
.cards__single--link {
  color: #000;
  transition: background-color 0.3s ease, color 0.3s ease;
}
.cards__single--link:hover {
  background-color: #575fcf;
  color: #fff;
  border-left-color: #3c40c6;
}
.cards__title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}
.dashboard__cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
  max-width: 80rem;
}
.dashboard__card {
  padding: 1rem 2rem 3.2rem;
  background-color: #fff;
  text-align: center;
}
.dashboard__status {
  text-align: right;
}
.dashboard__status--positive {
  color: green;
}
.dashboard__status--negative {
  color: red;
}
.dashboard__charts {
  padding: 2rem;
}

.login {
  display: grid;
  grid-template-columns: 1.3fr 1fr;
  grid-gap: 2rem;
  width: 100vw;
  height: 100vh;
}
@media screen and (max-width: 71.25em) {
  .login {
    grid-template-columns: 1fr 1.2fr;
  }
}
@media screen and (max-width: 51.875em) {
  .login {
    grid-template-columns: 1fr;
  }
}
.login__img-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.login__logo {
  width: 100%;
  height: auto;
  max-width: 60rem;
}
@media screen and (max-width: 51.875em) {
  .login__logo {
    width: auto;
    height: 30rem;
  }
}
.login__form-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  height: 100%;
  padding: 16rem 8rem 4rem;
  background-color: #fff;
  box-shadow: -10px 0 20px rgba(0, 0, 0, 0.1);
}
@media screen and (max-width: 31.25em) {
  .login__form-wrapper {
    padding: 6rem 3.2rem 4rem;
  }
}
.login__form {
  width: 100%;
  margin: 2rem 0;
}
.login__copyright {
  margin-top: auto;
}

.product__section {
  padding: 3.6rem;
  background-color: #fff;
}
.product__section:not(:last-child) {
  margin-bottom: 2.4rem;
}
.product__section-title {
  margin-bottom: 2rem;
}
.product__addon-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 1rem;
}
.product__addon-wrapper:not(:last-child) {
  margin-bottom: 1.6rem;
}
.product__addon-wrapper .input {
  min-width: 40rem;
}
.product__addon-wrapper .btn {
  margin-top: 0.8rem;
}
.product__images-wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
}
@media screen and (max-width: 28.75em) {
  .product__images-wrapper {
    grid-template-columns: 1fr;
  }
}
.product__img-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  grid-gap: 1.6rem;
}
.product__thumbnail {
  width: 100%;
  height: 100%;
  max-width: 12rem;
  max-height: 7rem;
}
.product__btn-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  margin-top: 3rem;
  padding-top: 3rem;
  border-top: 2px solid #575fcf;
}
.product__btns-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  margin-top: 3rem;
  padding-top: 3rem;
  border-top: 2px solid #575fcf;
}
.product__cards-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1.2rem;
}
@media screen and (max-width: 70em) {
  .product__cards-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 70em) {
  .product__cards-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 32.5em) {
  .product__cards-wrapper {
    grid-template-columns: 1fr;
  }
}

.product-card__img {
  width: 100%;
  height: 39rem;
}
.product-card__content {
  padding: 1.2rem 2rem 2rem;
  background-color: #fff;
}
.product-card__content .btn {
  margin-top: 1rem;
}

.discounts__wrapper {
  padding: 2.4rem;
  background-color: #fff;
}
.discounts__wrapper:not(:last-child) {
  margin-bottom: 2.4rem;
}
.discounts__header {
  margin-bottom: 2.4rem;
}
.discounts__form-wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
}
@media screen and (max-width: 29.375em) {
  .discounts__form-wrapper {
    grid-template-columns: 1fr;
  }
}
.discounts__form-wrapper .input:not(:last-child) {
  margin-bottom: 0;
}
.discounts__btn-wrapper {
  grid-column: 1/-1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.users__single {
  padding: 2.4rem;
  background-color: #fff;
  margin-bottom: 3.2rem;
}
.users__title-wrapper {
  margin-bottom: 2.4rem;
}
.users__stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 1rem;
}
@media screen and (max-width: 31.25em) {
  .users__stats {
    grid-template-columns: 1fr;
  }
}/*# sourceMappingURL=main.css.map */