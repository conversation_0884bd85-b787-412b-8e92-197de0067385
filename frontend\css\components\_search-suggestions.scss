// Search Suggestions Dropdown Styles
.search-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 4px;

  // Hide scrollbar but keep functionality
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #999;
  }
}

.search-suggestion {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;

  &:last-child {
    border-bottom: none;
  }

  &:hover,
  &.active {
    background-color: #f8f9fa;
    transform: translateX(2px);
  }

  &:focus {
    outline: 2px solid #007bff;
    outline-offset: -2px;
  }

  &__image {
    width: 50px;
    height: 50px;
    margin-right: 12px;
    border-radius: 6px;
    overflow: hidden;
    background: #f5f5f5;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  &__content {
    flex: 1;
    min-width: 0; // Allow text truncation
  }

  &__name {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.3;
    
    // Truncate long product names
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__category {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    line-height: 1.2;
  }

  &__price {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;

    .final-price {
      font-weight: 600;
      color: #007bff;
    }

    .original-price {
      text-decoration: line-through;
      color: #999;
      font-size: 12px;
    }

    .discount {
      background: #28a745;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
    }
  }
}

// Loading state
.search-suggestions-loading {
  padding: 16px;
  text-align: center;
  color: #666;
  font-size: 14px;

  &::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// No results state
.search-suggestions-empty {
  padding: 16px;
  text-align: center;
  color: #999;
  font-size: 14px;
  font-style: italic;
}

// Mobile responsive
@media (max-width: 768px) {
  .search-suggestions-dropdown {
    max-height: 300px;
    margin-top: 2px;
  }

  .search-suggestion {
    padding: 10px 12px;

    &__image {
      width: 40px;
      height: 40px;
      margin-right: 10px;
    }

    &__name {
      font-size: 13px;
    }

    &__category {
      font-size: 11px;
    }

    &__price {
      font-size: 12px;
      gap: 6px;

      .original-price {
        font-size: 11px;
      }

      .discount {
        font-size: 9px;
        padding: 1px 4px;
      }
    }
  }
}

// Dark mode support (if needed in future)
@media (prefers-color-scheme: dark) {
  .search-suggestions-dropdown {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }

  .search-suggestion {
    border-bottom-color: #4a5568;

    &:hover,
    &.active {
      background-color: #4a5568;
    }

    &__name {
      color: #e2e8f0;
    }

    &__category {
      color: #a0aec0;
    }

    &__price .final-price {
      color: #63b3ed;
    }
  }

  .search-suggestions-loading,
  .search-suggestions-empty {
    color: #a0aec0;
  }
}
