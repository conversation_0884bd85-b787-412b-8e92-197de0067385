// Search Suggestions Dropdown Styles
.search-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 4px;

  // Hide scrollbar but keep functionality
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #999;
  }
}

.search-suggestion {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
  min-height: 120px;

  &:last-child {
    border-bottom: none;
  }

  &:hover,
  &.active {
    background-color: #f8f9fa;
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:focus {
    outline: 2px solid #007bff;
    outline-offset: -2px;
  }

  &__image {
    width: 80px;
    height: 80px;
    margin-right: 16px;
    border-radius: 8px;
    overflow: hidden;
    background: #f5f5f5;
    flex-shrink: 0;
    border: 1px solid #e0e0e0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.2s ease;
    }
  }

  &:hover &__image img {
    transform: scale(1.05);
  }

  &__content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 12px;
  }

  &__name {
    font-weight: 600;
    font-size: 15px;
    color: #333;
    line-height: 1.3;
    flex: 1;

    // Allow wrapping for longer names
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  &__brand {
    font-size: 12px;
    color: #007bff;
    font-weight: 500;
    background: #e3f2fd;
    padding: 2px 8px;
    border-radius: 12px;
    white-space: nowrap;
  }

  &__description {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    margin: 4px 0;
  }

  &__details {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__category {
    font-size: 12px;
    color: #666;

    .category-label {
      font-weight: 500;
      color: #555;
    }
  }

  &__stock {
    font-size: 12px;

    .stock-label {
      font-weight: 500;
      color: #555;
    }

    &.in-stock {
      color: #28a745;
    }

    &.low-stock {
      color: #ffc107;
    }

    &.out-of-stock {
      color: #dc3545;
    }
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    gap: 12px;
  }

  &__price {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;

    .final-price {
      font-weight: 700;
      color: #007bff;
      font-size: 16px;
    }

    .original-price {
      text-decoration: line-through;
      color: #999;
      font-size: 13px;
    }

    .discount {
      background: #28a745;
      color: white;
      padding: 3px 8px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
    }
  }

  &__rating {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;

    .stars {
      display: flex;
      gap: 1px;

      .star {
        color: #ddd;
        font-size: 14px;

        &.filled {
          color: #ffc107;
        }
      }
    }

    .rating-value {
      color: #666;
      font-weight: 500;
    }
  }
}

// View All Results option
.search-suggestion-view-all {
  padding: 12px 16px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  border-top: 1px solid #e0e0e0;
  margin-top: 4px;

  &:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateX(2px);
  }

  .view-all-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 14px;
  }

  .view-all-text {
    flex: 1;
  }

  .view-all-arrow {
    font-size: 16px;
    transition: transform 0.2s ease;
  }

  &:hover .view-all-arrow {
    transform: translateX(4px);
  }
}

// Loading state
.search-suggestions-loading {
  padding: 16px;
  text-align: center;
  color: #666;
  font-size: 14px;

  &::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// No results state
.search-suggestions-empty {
  padding: 16px;
  text-align: center;
  color: #999;
  font-size: 14px;
  font-style: italic;
}

// Mobile responsive
@media (max-width: 768px) {
  .search-suggestions-dropdown {
    max-height: 350px;
    margin-top: 2px;
  }

  .search-suggestion {
    padding: 12px;
    min-height: 100px;

    &__image {
      width: 60px;
      height: 60px;
      margin-right: 12px;
    }

    &__content {
      gap: 6px;
    }

    &__name {
      font-size: 14px;
      -webkit-line-clamp: 1;
    }

    &__brand {
      font-size: 11px;
      padding: 1px 6px;
    }

    &__description {
      font-size: 12px;
      display: none; // Hide description on mobile to save space
    }

    &__details {
      gap: 2px;
    }

    &__category,
    &__stock {
      font-size: 11px;
    }

    &__footer {
      flex-direction: column;
      align-items: flex-start;
      gap: 6px;
    }

    &__price {
      font-size: 13px;
      gap: 6px;

      .final-price {
        font-size: 15px;
      }

      .original-price {
        font-size: 12px;
      }

      .discount {
        font-size: 9px;
        padding: 2px 6px;
      }
    }

    &__rating {
      font-size: 11px;
      gap: 4px;

      .stars .star {
        font-size: 12px;
      }
    }
  }
}

// Extra small screens
@media (max-width: 480px) {
  .search-suggestion {
    padding: 10px;
    min-height: 80px;

    &__image {
      width: 50px;
      height: 50px;
      margin-right: 10px;
    }

    &__name {
      font-size: 13px;
    }

    &__brand {
      font-size: 10px;
    }

    &__price .final-price {
      font-size: 14px;
    }
  }
}

// Dark mode support (if needed in future)
@media (prefers-color-scheme: dark) {
  .search-suggestions-dropdown {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }

  .search-suggestion {
    border-bottom-color: #4a5568;

    &:hover,
    &.active {
      background-color: #4a5568;
    }

    &__name {
      color: #e2e8f0;
    }

    &__category {
      color: #a0aec0;
    }

    &__price .final-price {
      color: #63b3ed;
    }
  }

  .search-suggestions-loading,
  .search-suggestions-empty {
    color: #a0aec0;
  }
}
